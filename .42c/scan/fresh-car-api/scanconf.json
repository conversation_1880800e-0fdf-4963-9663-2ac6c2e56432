{"version": "2.0.0", "runtimeConfiguration": {"environment": "default", "laxTestingModeEnabled": false, "apiConnectivityCheck": true, "logLevel": "error", "logDestination": "stdout+platform", "logMaxFileSize": 2147483648, "requestHeaderNameRequestId": "x-scan-request-id", "requestHeaderNameScenarioId": "x-scan-scenario-id", "requestHeaderNameRequestType": "x-scan-request-type", "requestFlowrate": 100, "requestTimeout": 30, "requestTlsInsecureSkipVerify": true, "responseFollowRedirection": false, "responseMaxBodySizeScan": 10485760, "happyPathOnly": false, "maxRequestRetryAttempts": 5, "maxScanDuration": 1800, "memoryLimit": 2147483648, "memoryTimeSpan": 10, "reportMaxRequestSizeHappyPath": 8092, "reportMaxRequestSizeTest": 8092, "reportIncludeRequestBody": true, "reportIncludeResponseBody": true, "reportMaxHttpResponseSizeHappyPath": 8092, "reportMaxBodySizeHappyPath": 8092, "reportMaxHttpResponseSizeTest": 8092, "reportMaxBodySizeTest": 8092, "reportIssuesOnly": false, "reportMaxIssues": 1000, "reportMaxSize": ********, "reportGenerateCurlCommand": true}, "customizations": {"happyPaths": {"retry": 1, "responsePolicy": {"httpStatusExpected": true, "mustBeConformant": true}, "httpStatusExpected": []}, "tests": {"responsePolicy": {"httpStatusExpected": true, "mustBeConformant": true}}}, "authenticationDetails": [{"bearer": {"type": "bearer", "default": "bearer", "credentials": {"bearer": {"description": "bearer security", "credential": "{{bearer}}"}}}}], "operations": {"/api/accounts/consent:put": {"operationId": "/api/accounts/consent:put", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts~1consent:put/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts/consent:put", "auth": ["bearer"], "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts/consent", "method": "PUT", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"emailConsent": false, "phoneConsent": false, "postalConsent": false, "smsConsent": true}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts/details:put": {"operationId": "/api/accounts/details:put", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts~1details:put/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts/details:put", "auth": ["bearer"], "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts/details", "method": "PUT", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"contactNumber": "gcfslgndijpfdmorousstaakhdx", "customerId": 7775823914409562253, "dateOfBirth": "wbuoavjcpcnrkpjbkuhbdrcaeecmgeku", "firstName": "kqmbwppjznkpbvwhvkqjhccvgogiacbqnt", "lastName": "fnrxufkmawznnavuxaqtnhpkkefychiumocvzmoqj", "title": "wvzuppbdit"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts/email-availability:post": {"operationId": "/api/accounts/email-availability:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts~1email-availability:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts/email-availability:post", "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts/email-availability", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"email": "nwfwvncvtrigjtlwvbsmvyysdc"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts/email-verification:post": {"operationId": "/api/accounts/email-verification:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts~1email-verification:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts/email-verification:post", "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts/email-verification", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"code": "jplty", "userId": "wihwwydiiuyqswvtegc"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts/password/forgot:post": {"operationId": "/api/accounts/password/forgot:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts~1password~1forgot:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts/password/forgot:post", "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts/password/forgot", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"email": "ncwmborrxzqriwqaibjnnxjyfxmhw"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts/password/reset:post": {"operationId": "/api/accounts/password/reset:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts~1password~1reset:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts/password/reset:post", "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts/password/reset", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"code": "qfspipqcgqgjjjhrpatzxlnhulh", "confirmPassword": "gzsacfnwtbzwfflutuudcylyxlqgrtxd", "email": "gfy", "password": "ocfywqfibacproktxnbdvreec"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts/password:put": {"operationId": "/api/accounts/password:put", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts~1password:put/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts/password:put", "auth": ["bearer"], "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts/password", "method": "PUT", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"confirmNewPassword": "uc", "email": "kzucjabqlwiw", "newPassword": "eqzdqqgvdkfspfusqsmeqsed", "oldPassword": "fjqrsa"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts:delete": {"operationId": "/api/accounts:delete", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts:delete/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts:delete", "auth": ["bearer"], "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts", "method": "DELETE"}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts:get": {"operationId": "/api/accounts:get", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts:get/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts:get", "auth": ["bearer"], "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts", "method": "GET"}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/accounts:post": {"operationId": "/api/accounts:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1accounts:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/accounts:post", "request": {"type": "42c", "details": {"url": "{{host}}/api/accounts", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"address": {"addressId": 6910437690523287529, "addressLine1": "ehyemabqfuohczkgcbwuankamhdgvgfzieqreygnbzrfyskflykrfunymflditujqmtmanjicgnczyambpzxehbnpjsbrtgtnvfpxyiloecuxlmwfusfhu", "addressLine2": "aakigaslrueutvqdznhqcxduzfdvjpwvcdlfdpvywotsuuguhniqcbtfktfjborfwfifjjeeorwhstqbertfeuotucakgihbslgkdfsecghhelnriwmwaixiztcufsrro", "addressLine3": "ahgyjpeultgwhnaqzfygckyfrncsapfzfosvgtefskgthbrvxxqervevjwdplgjimrcaqqfjqyofhylab", "country": 1, "postcode": "r", "town": "jyelzbea"}, "confirmPassword": "htuejoeepvavkvpjcrkryufyxcinbrju", "contactNumber": "qqwjaexhdcljbuieiihrwjlzhiljxykdjajugonbyxzckx", "customerId": 5928958038070937253, "customerReferenceNumber": "xhlamkjkpjeeljmdvpysqfpxtnnzmbna", "dateOfBirth": "vmfnkqhyxnareumuyeqokdpximxohrxp", "email": "bvfuhvepubucyjbevmfj", "firstName": "pwopxmfthbyabsrpfhorqmfgllbqifrpqrwshgtrf", "lastName": "jxdhirpxkcelyvchprhhtxmskbzbowamn", "password": "qszvrxlvlpaakgffxuyoo", "title": "uxwfezifez"}}}}, "defaultResponse": "201", "responses": {"201": {"expectations": {"httpStatus": 201}}}}}, "/api/auth/login:post": {"operationId": "/api/auth/login:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1auth~1login:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/auth/login:post", "request": {"type": "42c", "details": {"url": "{{host}}/api/auth/login", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"email": "pqsvz", "password": "hje"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/auth/logout:post": {"operationId": "/api/auth/logout:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1auth~1logout:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/auth/logout:post", "auth": ["bearer"], "request": {"type": "42c", "details": {"url": "{{host}}/api/auth/logout", "method": "POST"}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}, "/api/auth/refresh-token:post": {"operationId": "/api/auth/refresh-token:post", "scenarios": [{"key": "happy.path", "fuzzing": true, "requests": [{"$ref": "#/operations/~1api~1auth~1refresh-token:post/request", "fuzzing": true}]}], "request": {"operationId": "/api/auth/refresh-token:post", "auth": ["bearer"], "request": {"type": "42c", "details": {"url": "{{host}}/api/auth/refresh-token", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "requestBody": {"mode": "json", "json": {"email": "dmrnmne", "jwtToken": "itughaicohorxacvfihgvnoejneg"}}}}, "defaultResponse": "200", "responses": {"200": {"expectations": {"httpStatus": 200}}}}}}, "environments": {"default": {"variables": {"bearer": {"from": "environment", "name": "SCAN42C_SECURITY_BEARER", "required": true}, "host": {"from": "environment", "name": "SCAN42C_HOST", "required": true}}}}}