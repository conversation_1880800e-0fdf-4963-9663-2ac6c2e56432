name: CI

on:
  push:
    paths:
      - "**/.php"
      - "**/.yml"
      - "**/.json"
      - ".env.*"
    branches:
      - main
      - 11.0
  schedule:
    - cron: "0 0 * * *"

jobs:
  CI:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Docker Compose
        run: docker-compose -f ci-docker-compose.yml up -d
      - name: Setup dev environment
        run: docker-compose -f ci-docker-compose.yml exec -T api composer run dev
      - name: Pre Commit
        run: docker-compose -f ci-docker-compose.yml exec -T api composer run pre-commit
      - name: Pest tests
        run: docker-compose -f ci-docker-compose.yml exec -T api vendor/bin/pest
      - name: Postman tests
        run: docker-compose -f ci-docker-compose.yml exec -T api postman collection run postman/ci-collection.json
#
