using System.Threading.Tasks;
using FreshAPI.Interfaces;
using FreshAPI.Models.EndpointDTOs.NewCustomerBooking;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

namespace FreshAPI.Controllers;

[Authorize]
[Route("api/newcustomerbookings")]
public class NewCustomerBookingController : BaseController
{
    private readonly INewCustomerBookingService _newCustomerBookingService;

    public NewCustomerBookingController(INewCustomerBookingService newCustomerBookingService, IConfiguration configuration) : base(configuration)
    {
        _newCustomerBookingService = newCustomerBookingService;
    }

    [AllowAnonymous]
    [HttpPost]
    public async Task<IActionResult> AddNewCustomerBooking([FromBody] NewCustomerBookingDTO newCustomerBooking)
    {
        var responseModel = await _newCustomerBookingService.CreateNewCustomerBooking(newCustomerBooking);
        return BuildActionResult(responseModel);
    }
}
