<?php

namespace App\Console\Commands;

use App\Services\GoHighLevel\GoHighLevelRepository;
use Illuminate\Console\Command;

class GoHighLevelAutUrl extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:gohighlevel:auth-url {locationId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Proper URL by which, GoHighLevel will create callback url with the code with given id from 
    gohighlevel_locations table (from local database).';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $locationId = $this->argument('locationId');

        if (empty($locationId)) {
            $this->error('gohighlevel_locations.id as LocationId is required.');
            return Command::FAILURE;
        }

        /** @var GoHighLevelRepository */
        $gohighlevelService = resolve(GoHighLevelRepository::class);
        $this->info("Authorization URL: \n" . $gohighlevelService->getAuthorizationUrl((int) $locationId));

        return Command::SUCCESS;
    }
}
