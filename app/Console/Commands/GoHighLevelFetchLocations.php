<?php

namespace App\Console\Commands;

use App\Models\GohighlevelLocation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

class GoHighLevelFetchLocations extends Command
{
    protected $signature = 'app:gohighlevel:fetch-locations {--token= : Optional API token to override the one in .env} {--limit=100 : Number of locations to fetch per page} {--max-pages=10 : Maximum number of pages to fetch}';
    protected $description = 'Fetch all locations from GoHighLevel API and store them in the database';

    public function handle(): int
    {
        $token = $this->option('token') ?: config('services.gohighlevel.private_integration_token');

        if (empty($token)) {
            $message = 'API token is required. Set GOHIGHLEVEL_API_TOKEN in .env or provide --token option.';
            $this->error($message);
            Log::channel('gohighlevelService')->error($message);
            return Command::FAILURE;
        }

        $this->info('Fetching locations from GoHighLevel...');
        Log::channel('gohighlevelService')->info('Fetching locations from GoHighLevel...');

        try {
            $totalCount = 0;
            $hasMorePages = true;
            $skip = 0;
            $page = 1;
            $limit = (int)$this->option('limit');
            $maxPages = (int)$this->option('max-pages');
            while ($hasMorePages && $page <= $maxPages) {
                $this->info("Fetching page {$page} (skip: {$skip})...");
                Log::channel('gohighlevelService')->info("Fetching page {$page} (skip: {$skip})...");

                $apiUrl = config('services.gohighlevel.base_url_api') . '/locations/search';
                $queryParams = [
                    'limit' => $limit,
                    'skip' => $skip
                ];

                Log::channel('gohighlevelService')->info("Making request to: {$apiUrl} with params: " . json_encode($queryParams));

                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Authorization' => "Bearer {$token}",
                    'Version' => '2021-07-28'
                ])->get($apiUrl, $queryParams);

                // Log the full response regardless of success or failure
                Log::channel('gohighlevelService')->info("API Response Status: {$response->status()}");

                // Log the response body - handle both JSON and non-JSON responses
                $responseBody = $response->body();
                Log::channel('gohighlevelService')->info("API Response Body: {$responseBody}");

                if (!$response->successful()) {
                    $errorMessage = "API request failed: {$response->status()}";
                    $this->error($errorMessage);
                    Log::channel('gohighlevelService')->error($errorMessage);
                    return Command::FAILURE;
                }

                // Try to parse the response as JSON
                try {
                    $responseData = $response->json();

                    // Check if the response has a 'locations' key
                    if (!isset($responseData['locations']) || !is_array($responseData['locations'])) {
                        $message = "Unexpected response format. Expected object with 'locations' array, got: " . json_encode($responseData);
                        $this->error($message);
                        Log::channel('gohighlevelService')->error($message);
                        return Command::FAILURE;
                    }

                    $locations = $responseData['locations'];
                    $locationsCount = count($locations);
                    Log::channel('gohighlevelService')->info("Received {$locationsCount} locations from API on page {$page}");

                    // Process locations
                    $pageCount = 0;

                    foreach ($locations as $location) {
                        // Log each location for debugging
                        Log::channel('gohighlevelService')->debug("Processing location: " . json_encode($location));

                        if (!isset($location['id'])) {
                            Log::channel('gohighlevelService')->warning("Location missing ID, skipping: " . json_encode($location));
                            continue;
                        }

                        GohighlevelLocation::updateOrCreate(
                            ['location_id' => $location['id']],
                            [
                                'name' => $location['name'] ?? 'Unnamed Location',
                                'client_id' => config('services.gohighlevel.client_id'),
                                'client_secret' => config('services.gohighlevel.client_secret'),
                                'base_url_authorization' => config('services.gohighlevel.base_url_authorization'),
                                'base_url_api' => config('services.gohighlevel.base_url_api')
                            ]
                        );
                        $pageCount++;
                    }

                    $totalCount += $pageCount;
                    $this->info("Imported {$pageCount} locations from page {$page}. Total: {$totalCount}");

                    // If we received fewer locations than the limit or zero locations, we've reached the end
                    if ($locationsCount < $limit || $locationsCount === 0) {
                        $hasMorePages = false;
                    } else {
                        // Increment skip for the next page
                        $skip += $limit;
                        $page++;
                    }
                } catch (Throwable $jsonError) {
                    $message = "Failed to parse response as JSON: {$jsonError->getMessage()}";
                    $this->error($message);
                    Log::channel('gohighlevelService')->error($message);
                    return Command::FAILURE;
                }
            }

            $successMessage = "Successfully imported {$totalCount} locations from {$page} pages.";
            $this->info($successMessage);
            Log::channel('gohighlevelService')->info($successMessage);
            return Command::SUCCESS;
        } catch (Throwable $t) {
            $errorMessage = "Exception: {$t->getMessage()} in {$t->getFile()}:{$t->getLine()}";
            $this->error($errorMessage);
            Log::channel('gohighlevelService')->error($errorMessage, $t->getTrace());
            return Command::FAILURE;
        }
    }
}
