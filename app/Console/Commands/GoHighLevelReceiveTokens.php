<?php

namespace App\Console\Commands;

use App\Services\GoHighLevel\GoHighLevelRepository;
use Illuminate\Console\Command;
use Throwable;

class GoHighLevelReceiveTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:gohighlevel:receive-tokens {locationId} {code}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Receive and save access tokens for Gohighlevel api. It requires: \n
         - id from gohighlevel_locations table (from local database). \n
         - code from the callback url. You can get it by auth-url generated by app:gohighlevel:author-url command. \n";

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $code = $this->argument('code');
        $locationId = $this->argument('locationId');

        if (empty($code)) {
            $this->error('Code is required.');
            return Command::FAILURE;
        }

        if (empty($locationId)) {
            $this->error('gohighlevel_locations.id as LocationId is required.');
            return Command::FAILURE;
        }

        /** @var GoHighLevelRepository */
        $gohighlevelService = resolve(GoHighLevelRepository::class);
        try {
            $gohighlevelToken = $gohighlevelService->saveTokensByCode((int) $locationId, $code);
            $this->info('Received token for location: ' . $gohighlevelToken->getLocationId() . "\n");
        } catch (Throwable $t) {
            $this->error($t->getMessage());
        }
    }
}
