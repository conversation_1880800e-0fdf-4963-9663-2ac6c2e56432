<?php

namespace App\Console\Commands;

use App\Services\GoHighLevel\GoHighLevelRepository;
use Illuminate\Console\Command;
use Throwable;

class GoHighLevelRefreshTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:gohighlevel:refresh-tokens';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh and save access tokens for Gohighlevel api. It needs at least one valid refresh token, saved by receive-tokens command.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        /** @var GoHighLevelRepository */
        $gohighlevelService = resolve(GoHighLevelRepository::class);
        try {
            $processLocations = $gohighlevelService->refreshTokens();
            foreach ($processLocations as $locationToken) {
                $this->info('New access token saved. For Location: ' . $locationToken['data']['locationId']);
            }
        } catch (Throwable $t) {
            $this->error($t->getMessage(), $t->getTraceAsString());
        }
    }
}
