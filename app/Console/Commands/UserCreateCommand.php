<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;

class UserCreateCommand extends Command
{
    protected $signature = 'user:create 
                            {--email=<EMAIL> : User email}
                            {--first_name=Example : User first name}
                            {--last_name=Example : User last name}
                            {--password= : User password (required in non-interactive mode)}
                            {--role= : User role}
                            {--non-interactive : Run in non-interactive mode}';

    protected $description = 'Create User';

    public function handle(): void
    {
        // Check if we're running in non-interactive mode
        $nonInteractive = $this->option('non-interactive');
        
        // Get inputs either from options or by asking
        $email = $nonInteractive ? $this->option('email') : $this->ask('E-mail', $this->option('email'));
        $firstName = $nonInteractive ? $this->option('first_name') : $this->ask('First name', $this->option('first_name'));
        $lastName = $nonInteractive ? $this->option('last_name') : $this->ask('Last name', $this->option('last_name'));
        
        // For password, we need special handling in non-interactive mode
        $password = null;
        if ($nonInteractive) {
            $password = $this->option('password');
            if (empty($password)) {
                $this->error('The password field is required in non-interactive mode.');
                return;
            }
        } else {
            $password = $this->secret('Password');
        }
        
        // Get available roles
        $superAdmin = config('constants.roles.super_admin');
        $roles = array_values(config('constants.roles'));
        $rolesPrint = join(", ", $roles);
        
        // Get role from option or ask
        $role = $nonInteractive 
            ? ($this->option('role') ?: $superAdmin) 
            : $this->askWithCompletion("Role (possible: $rolesPrint)", $roles, $superAdmin);
        
        // Validate inputs
        $validator = Validator::make([
            'email' => $email,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'password' => $password
        ], [
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:' . User::class],
            'first_name' => ['required'],
            'last_name' => ['required'],
            'password' => ['required', Rules\Password::defaults()]
        ]);
        
        if ($validator->fails()) {
            $this->info('User not created. See error messages below:');
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return;
        }
        
        // Create the user
        $user = User::create([
            'email' => $email,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'password' => Hash::make($password),
            'email_verified_at' => now(),
            'remember_token' => Str::random(10),
        ]);
        
        $user->assignRole($role);
        
        $this->info("User created successfully:");
        $this->info("Email: {$email}");
        $this->info("Name: {$firstName} {$lastName}");
        $this->info("Role: {$role}");
    }
}
