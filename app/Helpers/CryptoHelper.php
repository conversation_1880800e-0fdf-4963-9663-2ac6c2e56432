<?php

namespace App\Helpers;

class CryptoHelper
{
    public static function verifySignature($payload, $signature, $publicKey): bool
    {
        // This method should only be called when signature is provided
        \Illuminate\Support\Facades\Log::channel('webhook')->info('crypto-helper:signature-verification:starting', [
            'signature_length' => strlen($signature ?? ''),
            'payload_length' => strlen($payload ?? ''),
            'public_key_length' => strlen($publicKey ?? '')
        ]);

        if (empty($signature) || empty($payload) || empty($publicKey)) {
            \Illuminate\Support\Facades\Log::channel('webhook')->error('crypto-helper:signature-verification:missing-data', [
                'has_signature' => !empty($signature),
                'has_payload' => !empty($payload),
                'has_public_key' => !empty($publicKey)
            ]);
            return false;
        }

        // Convert the base64 signature to binary
        $signatureBinary = base64_decode($signature);

        if ($signatureBinary === false) {
            \Illuminate\Support\Facades\Log::channel('webhook')->error('crypto-helper:signature-verification:base64-decode-failed', [
                'signature' => substr($signature, 0, 50) . '...'
            ]);
            return false;
        }

        // Use openssl to verify the signature
        $ok = openssl_verify($payload, $signatureBinary, $publicKey, OPENSSL_ALGO_SHA256);

        \Illuminate\Support\Facades\Log::channel('webhook')->info('crypto-helper:signature-verification:result', [
            'openssl_verify_result' => $ok,
            'is_valid' => $ok === 1,
            'openssl_error' => $ok === -1 ? openssl_error_string() : null
        ]);

        return $ok === 1;
    }
}
