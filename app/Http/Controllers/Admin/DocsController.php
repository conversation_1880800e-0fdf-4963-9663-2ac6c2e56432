<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\File;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class DocsController extends Controller
{
    private string $docsPath;

    public function __construct()
    {
        $this->docsPath = public_path('docs');
    }

    public function index(): Response|BinaryFileResponse
    {
        return $this->serveFile('index.html');
    }

    public function show(Request $request, string $path = ''): Response|BinaryFileResponse
    {
        // Clean the path to prevent directory traversal
        $path = $this->sanitizePath($path);

        // If no extension, try to serve as directory (look for index.html)
        if (!pathinfo($path, PATHINFO_EXTENSION)) {
            $indexPath = $path ? $path . '/index.html' : 'index.html';
            if (File::exists($this->docsPath . '/' . $indexPath)) {
                return $this->serveFile($indexPath);
            }
        }

        return $this->serveFile($path);
    }

    private function serveFile(string $path): Response|BinaryFileResponse
    {
        $fullPath = $this->docsPath . '/' . $path;

        if (!File::exists($fullPath)) {
            // If file doesn't exist, serve the main index.html for SPA routing
            $indexPath = $this->docsPath . '/index.html';
            if (File::exists($indexPath)) {
                return response()->file($indexPath);
            }

            abort(404, 'Documentation not found. Please build the documentation first.');
        }

        // Determine content type
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        $contentType = $this->getContentType($extension);

        if (in_array($extension, ['html', 'css', 'js', 'json'])) {
            // For text-based files, read content and return as response
            $content = File::get($fullPath);
            return response($content)->header('Content-Type', $contentType);
        }

        // For binary files (images, fonts, etc.), use BinaryFileResponse
        return response()->file($fullPath);
    }

    private function sanitizePath(string $path): string
    {
        // Remove any attempts at directory traversal
        $path = str_replace(['../', '..\\', '../', '..\\'], '', $path);

        // Remove leading slashes
        $path = ltrim($path, '/\\');

        return $path;
    }

    private function getContentType(string $extension): string
    {
        $mimeTypes = [
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
