<?php

namespace App\Http\Controllers\Admin;

use App\Enums\NotificationEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Gohighlevel\CodeLocationRequest;
use App\Http\Requests\Gohighlevel\CreateLocationRequest;
use App\Http\Requests\Gohighlevel\RefreshLocationTokenRequest;
use App\Http\Requests\Gohighlevel\UpdateLocationRequest;
use App\Models\GohighlevelLocation;
use App\Services\GoHighLevel\GoHighLevelRepository;
use App\ValueObjects\Admin\NotificationVO;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;
use Throwable;
use Artisan;

class GohighlevelController extends Controller
{
    public function locations(): View
    {
        /** @var GoHighLevelRepository $gohighRepository */
        $gohighRepository = resolve(GoHighLevelRepository::class);

        $locations = GohighlevelLocation::get();
        $data = [];
        foreach ($locations as $location) {
            if (!empty($location->private_integration_key)) {
                $data[$location->id] = [
                    'authUrl' => $gohighRepository->getAuthorizationUrl($location->id),
                    'token' => true,
                    'isValid' => true,
                    'isPrivateIntegration' => true,
                    'redirectUri' => $gohighRepository->getRedirectUri($location->id)
                ];
            } else {
                $lastToken = $gohighRepository->getLastTokenRow($location->id);
                $data[$location->id] = [
                    'authUrl' => $gohighRepository->getAuthorizationUrl($location->id),
                    'token' => $lastToken ? true : false,
                    'isValid' => $lastToken ? $gohighRepository->isTokenRowValid($lastToken) : false,
                    'isPrivateIntegration' => false,
                    'redirectUri' => $gohighRepository->getRedirectUri($location->id)
                ];
            }
        }


        return view('gohighlevel.locations', [
            'locations' => $locations,
            'data' => $data
        ]);
    }


    /**
     * @throws \App\Exceptions\ForbiddenException
     */
    public function destroyLocation(Request $request): RedirectResponse
    {
        if (!$request->has('id')) {
            return Redirect::route('admin.gohighlevel.locations');
        }
        $id = $request->id ?? null;
        $location = GohighlevelLocation::find($id);
        $location->tokens()->delete();
        $location->delete();

        return Redirect::route('admin.gohighlevel.locations')->with(
            'notification',
            new NotificationVO(
                NotificationEnum::SUCCESS,
                __('messages.notification.successfully_deleted'),
                __('messages.gohighlevel.locations.deleted')
            )
        );
    }

    public function codeLocation(CodeLocationRequest $request): RedirectResponse
    {
        $gohighlevelRep = resolve(GoHighLevelRepository::class);
        try {
            $gohighlevelRep->saveTokensByCode($request->location_id, $request->code);
            return Redirect::route('admin.gohighlevel.locations')->with(
                'notification',
                new NotificationVO(
                    NotificationEnum::SUCCESS,
                    __('messages.notification.successfully_updated'),
                    __('messages.gohighlevel.locations.tokens_saved')
                )
            );
        } catch (Throwable $t) {
            Log::error($t->getMessage(), $t->getTrace());
            return Redirect::route('admin.gohighlevel.locations')->with(
                'notification',
                new NotificationVO(
                    NotificationEnum::FAIL,
                    __('messages.notification.fail'),
                    __('messages.gohighlevel.locations.fail')
                )
            );
        }
    }

    public function refreshLocationToken(RefreshLocationTokenRequest $request): RedirectResponse
    {
        $gohighlevelRep = resolve(GoHighLevelRepository::class);
        $locationId = $request->validated('location_id');
        try {
            $gohighlevelRep->refreshLocationToken((int) $locationId);
            return Redirect::route('admin.gohighlevel.locations')->with(
                'notification',
                new NotificationVO(
                    NotificationEnum::SUCCESS,
                    __('messages.notification.successfully_updated'),
                    __('messages.gohighlevel.locations.tokens_saved')
                )
            );
        } catch (Throwable $t) {
            return Redirect::route('admin.gohighlevel.locations')->with(
                'notification',
                new NotificationVO(
                    NotificationEnum::FAIL,
                    __('messages.notification.fail'),
                    __('messages.gohighlevel.locations.fail')
                )
            );
        }
    }

    public function createLocation(): View
    {
        $config = config('services.gohighlevel');

        return view('gohighlevel.createLocation', [
            'base_url_authorization' => $config['base_url_authorization'],
            'base_url_api' => $config['base_url_api']
        ]);
    }

    public function editLocation(GohighlevelLocation $location): View
    {

        return view('gohighlevel.editLocation', [
            'location' => $location
        ]);
    }

    public function storeLocation(CreateLocationRequest $request): RedirectResponse
    {
        $data = $request->validated();

        // Set empty strings for client_id and client_secret if they're not provided
        $data['client_id'] = $data['client_id'] ?? '';
        $data['client_secret'] = $data['client_secret'] ?? '';

        // If using private_integration_key, ensure client_id and client_secret are empty
        if (!empty($data['private_integration_key'])) {
            $data['client_id'] = '';
            $data['client_secret'] = '';
        }

        $location = GohighlevelLocation::create($data);

        $redirect = Redirect::route('admin.gohighlevel.locations',)->with(
            'notification',
            new NotificationVO(
                NotificationEnum::SUCCESS,
                __('messages.notification.successfully_created'),
                __('messages.gohighlevel.locations.created')
            )
        );

        return $redirect;
    }

    public function updateLocation(UpdateLocationRequest $request, GohighlevelLocation $location): RedirectResponse
    {
        $data = $request->validated();

        // Set empty strings for client_id and client_secret if they're not provided
        $data['client_id'] = $data['client_id'] ?? '';
        $data['client_secret'] = $data['client_secret'] ?? '';

        // If using private_integration_key, ensure client_id and client_secret are empty
        if (!empty($data['private_integration_key'])) {
            $data['client_id'] = '';
            $data['client_secret'] = '';
        }

        $location->update($data);
        $location->save();

        $redirect = Redirect::route('admin.gohighlevel.locations',)->with(
            'notification',
            new NotificationVO(
                NotificationEnum::SUCCESS,
                __('messages.notification.successfully_updated'),
                __('messages.gohighlevel.locations.updated')
            )
        );

        return $redirect;
    }

    public function fetchLocations(Request $request): RedirectResponse
    {
        try {
            $exitCode = Artisan::call('app:gohighlevel:fetch-locations');

            if ($exitCode === 0) {
                return Redirect::route('admin.gohighlevel.locations')->with(
                    'notification',
                    new NotificationVO(
                        NotificationEnum::SUCCESS,
                        __('messages.notification.successfully_created'),
                        __('messages.gohighlevel.locations.imported')
                    )
                );
            } else {
                return Redirect::route('admin.gohighlevel.locations')->with(
                    'notification',
                    new NotificationVO(
                        NotificationEnum::FAIL,
                        __('messages.notification.fail'),
                        __('messages.gohighlevel.locations.import_failed')
                    )
                );
            }
        } catch (Throwable $t) {
            Log::error($t->getMessage(), $t->getTrace());
            return Redirect::route('admin.gohighlevel.locations')->with(
                'notification',
                new NotificationVO(
                    NotificationEnum::FAIL,
                    __('messages.notification.fail'),
                    __('messages.gohighlevel.locations.fail')
                )
            );
        }
    }

    public function importLocationsForm(): View
    {
        return view('gohighlevel.importLocations');
    }
}
