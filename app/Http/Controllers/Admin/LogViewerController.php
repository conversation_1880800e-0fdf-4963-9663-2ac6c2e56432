<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class LogViewerController extends Controller
{
    public function index(Request $request): View
    {
        $logPath = storage_path('logs');
        $logFiles = collect(File::files($logPath))
            ->filter(function ($file) {
                return pathinfo($file, PATHINFO_EXTENSION) === 'log';
            })
            ->map(function ($file) {
                return [
                    'name' => $file->getFilename(),
                    'size' => $this->formatFileSize($file->getSize()),
                    'modified' => date('Y-m-d H:i:s', $file->getMTime()),
                ];
            })
            ->sortByDesc('modified')
            ->values();

        $selectedLog = $request->input('file', $logFiles->first()['name'] ?? null);
        $lines = (int) $request->input('lines', 100);

        $content = null;
        if ($selectedLog) {
            $fullPath = $logPath . '/' . $selectedLog;
            if (File::exists($fullPath)) {
                $content = $this->getLogContent($fullPath, $lines);
            }
        }

        return view('admin.logs.viewer', [
            'logFiles' => $logFiles,
            'selectedLog' => $selectedLog,
            'content' => $content,
            'lines' => $lines,
        ]);
    }

    public function content(Request $request): string
    {
        $fileName = $request->input('file');
        $lines = (int) $request->input('lines', 100);
        $logPath = storage_path('logs/' . $fileName);

        if (!File::exists($logPath)) {
            return 'Log file not found';
        }

        return $this->getLogContent($logPath, $lines);
    }

    public function download(Request $request): BinaryFileResponse
    {
        $fileName = $request->input('file');
        $logPath = storage_path('logs/' . $fileName);

        if (!File::exists($logPath)) {
            abort(404, 'Log file not found');
        }

        return Response::download($logPath, $fileName);
    }

    private function getLogContent(string $path, int $lines): string
    {
        // Use tail command for efficiency with large files
        $content = shell_exec("tail -n {$lines} {$path}");

        // If shell_exec is disabled, fallback to PHP implementation
        if ($content === null) {
            $content = $this->tailFile($path, $lines);
        }

        return $content ?: 'Log file is empty';
    }

    private function tailFile(string $path, int $lines): string
    {
        $file = fopen($path, 'r');
        $total_lines = count(file($path));
        $lineCounter = 0;
        $content = [];

        while (!feof($file)) {
            $line = fgets($file);
            $lineCounter++;

            if ($lineCounter > $total_lines - $lines) {
                $content[] = $line;
            }
        }

        fclose($file);
        return implode('', $content);
    }

    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = 0;
        while ($bytes > 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
