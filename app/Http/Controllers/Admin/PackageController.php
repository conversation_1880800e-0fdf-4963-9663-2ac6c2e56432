<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Package\CreatePackageRequest;
use App\Http\Requests\Admin\Package\PackageListRequest;
use App\Http\Requests\Admin\Package\UpdatePackageRequest;
use App\Http\Resources\PackageResource;
use App\Http\Resources\ServiceGroupResource;
use App\Models\Package;
use App\Services\Contracts\PackageServiceInterface;
use App\Services\Contracts\ServiceGroupServiceInterface;
use App\Traits\ActivityLog;
use App\Traits\HttpResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Exception;

class PackageController extends Controller
{
    use ActivityLog;
    use HttpResponse;

    public function __construct(
        private readonly PackageServiceInterface $packageService,
        private readonly ServiceGroupServiceInterface $serviceGroupService
    ) {
    }

    public function index(PackageListRequest $request): View
    {
        $sortBy = $request->validated('sortBy') ?? 'sort_order';
        $orderBy = $request->validated('orderBy') ?? 'asc';
        $perPage = $request->validated('perPage', config('constants.pagination.default_per_page', 15));

        $filters = $request->only(['search', 'service_group_id', 'is_active', 'price_min', 'price_max']);

        $packages = $this->packageService->getAllPackages($filters, $perPage);
        $serviceGroups = $this->serviceGroupService->getAllActiveServiceGroups();

        return view('admin.packages.index', [
            'packages' => PackageResource::collection($packages),
            'serviceGroups' => ServiceGroupResource::collection($serviceGroups),
            'filters' => $filters,
        ]);
    }

    public function create(): View
    {
        $serviceGroups = $this->serviceGroupService->getAllActiveServiceGroups();

        return view('admin.packages.create', [
            'serviceGroups' => ServiceGroupResource::collection($serviceGroups),
        ]);
    }

    public function store(CreatePackageRequest $request): RedirectResponse
    {
        try {
            $package = $this->packageService->createPackage($request->validated());

            $this->activity('Package Created', $package, Auth::user());

            return redirect()
                ->route('admin.packages.index')
                ->with('success', 'Package created successfully.');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create package: ' . $e->getMessage()]);
        }
    }

    public function show(Package $package): View
    {
        $package->load('serviceGroup', 'carClubSubscriptions');

        return view('admin.packages.show', [
            'package' => new PackageResource($package),
        ]);
    }

    public function edit(Package $package): View
    {
        $serviceGroups = $this->serviceGroupService->getAllActiveServiceGroups();

        return view('admin.packages.edit', [
            'package' => new PackageResource($package),
            'serviceGroups' => ServiceGroupResource::collection($serviceGroups),
        ]);
    }

    public function update(UpdatePackageRequest $request, Package $package): RedirectResponse
    {
        try {
            $updatedPackage = $this->packageService->updatePackage($package, $request->validated());

            $this->activity('Package Updated', $updatedPackage, Auth::user());

            return redirect()
                ->route('admin.packages.index')
                ->with('success', 'Package updated successfully.');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update package: ' . $e->getMessage()]);
        }
    }

    public function destroy(Package $package): RedirectResponse
    {
        try {
            $this->packageService->deletePackage($package);

            $this->activity('Package Deleted', $package, Auth::user());

            return redirect()
                ->route('admin.packages.index')
                ->with('success', 'Package deleted successfully.');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to delete package: ' . $e->getMessage()]);
        }
    }

    public function toggleStatus(Package $package): RedirectResponse
    {
        try {
            $updatedPackage = $this->packageService->toggleActiveStatus($package);

            $status = $updatedPackage->is_active ? 'activated' : 'deactivated';
            $this->activity("Package {$status}", $updatedPackage, Auth::user());

            return redirect()
                ->back()
                ->with('success', "Package {$status} successfully.");
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to toggle package status: ' . $e->getMessage()]);
        }
    }

    public function duplicate(Package $package): RedirectResponse
    {
        try {
            $duplicatedPackage = $this->packageService->duplicatePackage($package);

            $this->activity('Package Duplicated', $duplicatedPackage, Auth::user());

            return redirect()
                ->route('admin.packages.edit', $duplicatedPackage)
                ->with('success', 'Package duplicated successfully. Please review and update the details.');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to duplicate package: ' . $e->getMessage()]);
        }
    }
}
