<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ServiceGroup\CreateServiceGroupRequest;
use App\Http\Requests\Admin\ServiceGroup\ServiceGroupListRequest;
use App\Http\Requests\Admin\ServiceGroup\UpdateServiceGroupRequest;
use App\Http\Resources\ServiceGroupResource;
use App\Models\ServiceGroup;
use App\Services\Contracts\ServiceGroupServiceInterface;
use App\Traits\ActivityLog;
use App\Traits\HttpResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Exception;

class ServiceGroupController extends Controller
{
    use ActivityLog;
    use HttpResponse;

    public function __construct(
        private readonly ServiceGroupServiceInterface $serviceGroupService
    ) {
    }

    public function index(ServiceGroupListRequest $request): View
    {
        $sortBy = $request->validated('sortBy') ?? 'sort_order';
        $orderBy = $request->validated('orderBy') ?? 'asc';
        $perPage = $request->validated('perPage', config('constants.pagination.default_per_page', 15));

        $filters = $request->only(['search', 'is_active']);

        $serviceGroups = $this->serviceGroupService->getAllServiceGroups($filters, $perPage);

        return view('admin.service-groups.index', [
            'serviceGroups' => ServiceGroupResource::collection($serviceGroups),
            'filters' => $filters,
        ]);
    }

    public function create(): View
    {
        return view('admin.service-groups.create');
    }

    public function store(CreateServiceGroupRequest $request): RedirectResponse
    {
        try {
            $serviceGroup = $this->serviceGroupService->createServiceGroup($request->validated());

            $this->activity('Service Group Created', $serviceGroup, Auth::user());

            return redirect()
                ->route('admin.service-groups.index')
                ->with('success', 'Service group created successfully.');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create service group: ' . $e->getMessage()]);
        }
    }

    public function show(ServiceGroup $serviceGroup): View
    {
        $serviceGroup->load('packages');

        return view('admin.service-groups.show', [
            'serviceGroup' => new ServiceGroupResource($serviceGroup),
        ]);
    }

    public function edit(ServiceGroup $serviceGroup): View
    {
        return view('admin.service-groups.edit', [
            'serviceGroup' => new ServiceGroupResource($serviceGroup),
        ]);
    }

    public function update(UpdateServiceGroupRequest $request, ServiceGroup $serviceGroup): RedirectResponse
    {
        try {
            $updatedServiceGroup = $this->serviceGroupService->updateServiceGroup($serviceGroup, $request->validated());

            $this->activity('Service Group Updated', $updatedServiceGroup, Auth::user());

            return redirect()
                ->route('admin.service-groups.index')
                ->with('success', 'Service group updated successfully.');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update service group: ' . $e->getMessage()]);
        }
    }

    public function destroy(ServiceGroup $serviceGroup): RedirectResponse
    {
        try {
            $this->serviceGroupService->deleteServiceGroup($serviceGroup);

            $this->activity('Service Group Deleted', $serviceGroup, Auth::user());

            return redirect()
                ->route('admin.service-groups.index')
                ->with('success', 'Service group deleted successfully.');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to delete service group: ' . $e->getMessage()]);
        }
    }

    public function toggleStatus(ServiceGroup $serviceGroup): RedirectResponse
    {
        try {
            $updatedServiceGroup = $this->serviceGroupService->toggleActiveStatus($serviceGroup);

            $status = $updatedServiceGroup->is_active ? 'activated' : 'deactivated';
            $this->activity("Service Group {$status}", $updatedServiceGroup, Auth::user());

            return redirect()
                ->back()
                ->with('success', "Service group {$status} successfully.");
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to toggle service group status: ' . $e->getMessage()]);
        }
    }
}
