<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\WebhookMonitoringRequest;
use App\Http\Resources\WebhookActivityResource;
use App\Services\WebhookActivityService;
use App\Services\WebhookMetricsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Spatie\Activitylog\Models\Activity;

class WebhookMonitoringController extends Controller
{
    public function __construct(
        private WebhookMetricsService $metricsService,
        private WebhookActivityService $activityService
    ) {
    }

    public function index(WebhookMonitoringRequest $request): View
    {
        $period = $request->validated('period', 'today');
        $sortBy = $request->validated('sortBy', 'created_at');
        $orderBy = $request->validated('orderBy', 'desc');

        // Get metrics based on selected period
        $metrics = match ($period) {
            'week' => $this->metricsService->getWeeklyMetrics(),
            'month' => $this->metricsService->getMonthlyMetrics(),
            default => $this->metricsService->getTodayMetrics(),
        };

        // Get signature validation metrics
        $signatureMetrics = $this->metricsService->getSignatureValidationMetrics(
            $this->getPeriodStartDate($period)
        );

        // Get hourly volume data for chart
        $hourlyData = $this->metricsService->getHourlyVolumeData(
            $this->getPeriodStartDate($period)
        );

        // Get recent webhook activity
        $recentActivity = $this->metricsService->getRecentWebhookActivity(50);

        // Get webhook activities with pagination
        $activities = Activity::whereIn('description', [
            WebhookActivityService::LOG_WEBHOOK_RECEIVED,
            WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED,
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED,
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED,
            WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK,
            WebhookActivityService::LOG_WEBHOOK_BOOKING_CREATED,
        ])
            ->when($period !== 'all', function ($query) use ($period) {
                $query->where('created_at', '>=', $this->getPeriodStartDate($period));
            })
            ->orderBy($sortBy, $orderBy)
            ->paginate(20)
            ->withQueryString();

        // Transform the paginated activities through the resource and convert to arrays
        $activities->through(function ($activity) {
            return (object) (new WebhookActivityResource($activity))->toArray(request());
        });

        return view('admin.webhook-monitoring.index', [
            'metrics' => $metrics,
            'signatureMetrics' => $signatureMetrics,
            'hourlyData' => $hourlyData,
            'recentActivity' => WebhookActivityResource::collection($recentActivity),
            'activities' => $activities,
            'period' => $period,
            'signatureStates' => $this->activityService->getSignatureValidationStates(),
        ]);
    }

    public function show(string $webhookGroupId): View
    {
        // Get all activities for this webhook group ID
        $activities = Activity::whereIn('description', [
            WebhookActivityService::LOG_WEBHOOK_RECEIVED,
            WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED,
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_STARTED,
            WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK,
            WebhookActivityService::LOG_WEBHOOK_BOOKING_CREATED,
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED,
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED,
        ])
        ->get()
        ->filter(function ($activity) use ($webhookGroupId) {
            $properties = [];
            if ($activity->properties instanceof \Illuminate\Support\Collection) {
                $properties = $activity->properties->toArray();
            } elseif (is_array($activity->properties)) {
                $properties = $activity->properties;
            }

            // Check both GoHighLevel webhook ID and our internal webhook ID
            $activityWebhookGroupId = $properties['ghl_webhook_id'] ?? $properties['webhook_id'] ?? null;
            return $activityWebhookGroupId == $webhookGroupId;
        })
        ->sortBy('created_at')
        ->values();

        if ($activities->isEmpty()) {
            abort(404, 'Webhook processing flow not found');
        }

        // Transform activities through the resource
        $transformedActivities = $activities->map(function ($activity) {
            return (object) (new WebhookActivityResource($activity))->toArray(request());
        });

        // Get the first activity to extract webhook information
        $firstActivity = $transformedActivities->first();

        // Find customer email from any activity that has it
        $customerEmail = $transformedActivities
            ->filter(fn ($activity) => !empty($activity->customer_email))
            ->first()
            ->customer_email ?? null;

        return view('admin.webhook-monitoring.show', [
            'activities' => $transformedActivities,
            'webhookGroupId' => $webhookGroupId,
            'webhookInfo' => [
                'webhook_name' => $firstActivity->webhook_name ?? 'Unknown',
                'location_display' => $firstActivity->location_display ?? 'Unknown Location',
                'customer_email' => $customerEmail,
                'ghl_location_id' => $firstActivity->ghl_location_id ?? null,
                'ghl_location_name' => $firstActivity->ghl_location_name ?? null,
            ],
        ]);
    }

    public function metrics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'today');

        $metrics = match ($period) {
            'week' => $this->metricsService->getWeeklyMetrics(),
            'month' => $this->metricsService->getMonthlyMetrics(),
            default => $this->metricsService->getTodayMetrics(),
        };

        $signatureMetrics = $this->metricsService->getSignatureValidationMetrics(
            $this->getPeriodStartDate($period)
        );

        return response()->json([
            'metrics' => $metrics,
            'signatureMetrics' => $signatureMetrics,
            'period' => $period,
        ]);
    }

    public function chartData(Request $request): JsonResponse
    {
        $period = $request->get('period', 'today');

        $hourlyData = $this->metricsService->getHourlyVolumeData(
            $this->getPeriodStartDate($period)
        );

        return response()->json([
            'hourlyData' => $hourlyData,
            'period' => $period,
        ]);
    }

    private function getPeriodStartDate(string $period): Carbon
    {
        return match ($period) {
            'week' => Carbon::now()->startOfWeek(),
            'month' => Carbon::now()->startOfMonth(),
            'all' => Carbon::now()->subYear(),
            default => Carbon::today(),
        };
    }
}
