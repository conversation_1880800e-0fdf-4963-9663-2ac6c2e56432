<?php

namespace App\Http\Controllers\v3;

use App\Http\Controllers\Controller;
use App\Http\Resources\CarCategoryResource;
use App\Services\Contracts\CarCategoryServiceInterface;
use App\Traits\HttpResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CarCategoryController extends Controller
{
    use HttpResponse;

    public function __construct(
        private readonly CarCategoryServiceInterface $carCategoryService
    ) {
    }

    public function index(): AnonymousResourceCollection
    {
        $categories = $this->carCategoryService->getAllActiveCategories();

        return CarCategoryResource::collection($categories);
    }
}
