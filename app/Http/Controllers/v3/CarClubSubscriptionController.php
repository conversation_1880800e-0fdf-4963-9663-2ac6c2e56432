<?php

namespace App\Http\Controllers\v3;

use App\Http\Controllers\Controller;
use App\Http\Requests\CancelSubscriptionRequest;
use App\Http\Requests\CreateSubscriptionRequest;
use App\Http\Requests\PauseSubscriptionRequest;
use App\Http\Requests\ResumeSubscriptionRequest;
use App\Http\Resources\CarClubSubscriptionResource;
use App\Services\Contracts\CarClubSubscriptionServiceInterface;
use App\Traits\HttpResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response;
use Exception;
use InvalidArgumentException;
use Log;

class CarClubSubscriptionController extends Controller
{
    use HttpResponse;

    public function __construct(
        private readonly CarClubSubscriptionServiceInterface $subscriptionService
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * List all car club subscriptions for the authenticated user.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $perPage = $request->get('perPage', 15);
        $perPage = min(max((int) $perPage, 1), 100); // Ensure between 1 and 100

        $subscriptions = $this->subscriptionService->getUserSubscriptions(
            auth()->user(),
            $perPage
        );

        return CarClubSubscriptionResource::collection($subscriptions);
    }

    /**
     * Create a new car club subscription.
     */
    public function store(CreateSubscriptionRequest $request): JsonResponse
    {
        try {
            $subscription = $this->subscriptionService->createSubscription(
                auth()->user(),
                $request->validated()
            );

            return $this->resourceResponse(
                new CarClubSubscriptionResource($subscription),
                'Car club subscription created successfully',
                Response::HTTP_CREATED
            );
        } catch (InvalidArgumentException $e) {
            return $this->response(
                null,
                $e->getMessage(),
                Response::HTTP_BAD_REQUEST
            );
        } catch (Exception $e) {
            Log::error('Failed to create car club subscription', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->response(
                null,
                'An error occurred while creating the subscription',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel a car club subscription.
     */
    public function destroy(int $id, CancelSubscriptionRequest $request): JsonResponse
    {
        try {
            $success = $this->subscriptionService->cancelSubscription(
                auth()->user(),
                $id,
                $request->validated()
            );

            if (!$success) {
                return $this->response(
                    null,
                    'Subscription not found or cannot be cancelled',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->response(
                null,
                'Subscription cancelled successfully',
                Response::HTTP_OK
            );
        } catch (Exception $e) {
            Log::error('Failed to cancel car club subscription', [
                'user_id' => auth()->id(),
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return $this->response(
                null,
                'An error occurred while cancelling the subscription',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Pause a car club subscription.
     */
    public function pause(int $id, PauseSubscriptionRequest $request): JsonResponse
    {
        try {
            $success = $this->subscriptionService->pauseSubscription(
                auth()->user(),
                $id,
                $request->validated()
            );

            if (!$success) {
                return $this->response(
                    null,
                    'Subscription not found or cannot be paused',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->response(
                null,
                'Subscription paused successfully',
                Response::HTTP_OK
            );
        } catch (Exception $e) {
            Log::error('Failed to pause car club subscription', [
                'user_id' => auth()->id(),
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return $this->response(
                null,
                'An error occurred while pausing the subscription',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Resume a paused car club subscription.
     */
    public function resume(int $id, ResumeSubscriptionRequest $request): JsonResponse
    {
        try {
            $success = $this->subscriptionService->resumeSubscription(
                auth()->user(),
                $id,
                $request->validated()
            );

            if (!$success) {
                return $this->response(
                    null,
                    'Subscription not found or cannot be resumed',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->response(
                null,
                'Subscription resumed successfully',
                Response::HTTP_OK
            );
        } catch (Exception $e) {
            Log::error('Failed to resume car club subscription', [
                'user_id' => auth()->id(),
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return $this->response(
                null,
                'An error occurred while resuming the subscription',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
