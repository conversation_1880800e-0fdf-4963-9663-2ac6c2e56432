<?php

namespace App\Http\Controllers\v3;

use App\Http\Controllers\Controller;
use App\Http\Requests\CarListRequest;
use App\Http\Requests\CreateCarRequest;
use App\Http\Requests\UpdateCarRequest;
use App\Http\Resources\CarResource;
use App\Services\Contracts\CarServiceInterface;
use App\Traits\HttpResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response;
use Exception;

class CarController extends Controller
{
    use HttpResponse;

    public function __construct(
        private readonly CarServiceInterface $carService
    ) {
        $this->middleware('auth:sanctum');
    }

    public function index(CarListRequest $request): AnonymousResourceCollection
    {
        $filters = $request->validated();
        $perPage = $filters['perPage'] ?? 15;

        $cars = $this->carService->getUserCars(auth()->user(), $filters, $perPage);

        return CarResource::collection($cars);
    }

    public function show(int $id): JsonResponse
    {
        $car = $this->carService->findUserCar(auth()->user(), $id);

        if (!$car) {
            return $this->response(
                null,
                'Car not found',
                Response::HTTP_NOT_FOUND
            );
        }

        return $this->resourceResponse(new CarResource($car));
    }

    public function store(CreateCarRequest $request): JsonResponse
    {
        try {
            $car = $this->carService->createCar(
                auth()->user(),
                $request->validated()
            );

            return $this->resourceResponse(
                new CarResource($car),
                'Car created successfully',
                Response::HTTP_CREATED
            );
        } catch (Exception $e) {
            return $this->response(
                null,
                'An error occurred while creating the car',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function update(UpdateCarRequest $request, int $id): JsonResponse
    {
        try {
            $car = $this->carService->updateCar(
                auth()->user(),
                $id,
                $request->validated()
            );

            if (!$car) {
                return $this->response(
                    null,
                    'Car not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->resourceResponse(
                new CarResource($car),
                'Car updated successfully',
                Response::HTTP_OK
            );
        } catch (Exception $e) {
            return $this->response(
                null,
                'An error occurred while updating the car',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function destroy(int $id): JsonResponse
    {
        $deleted = $this->carService->deleteCar(auth()->user(), $id);

        if (!$deleted) {
            return $this->response(
                null,
                'Car not found',
                Response::HTTP_NOT_FOUND
            );
        }

        return $this->response(
            null,
            'Car deleted successfully',
            Response::HTTP_OK
        );
    }
}
