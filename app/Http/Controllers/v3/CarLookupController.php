<?php

namespace App\Http\Controllers\v3;

use App\Http\Controllers\Controller;
use App\Http\Requests\CarLookup\LookupCarByRegistrationRequest;
use App\Http\Requests\CarLookup\SearchCarsByMakeModelRequest;
use App\Http\Resources\CarLookupResource;
use App\Http\Resources\CarSearchResultResource;
use App\Http\Resources\CarSearchResultCollection;
use App\Services\Contracts\CarLookupServiceInterface;
use App\Traits\HttpResponse;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Exception;

/**
 * CarLookupController handles public car lookup operations.
 *
 * This controller provides endpoints for:
 * - Looking up car details by registration number
 * - Searching cars by make and model
 *
 * All endpoints are public and do not require authentication.
 */
class CarLookupController extends Controller
{
    use HttpResponse;

    public function __construct(
        private readonly CarLookupServiceInterface $carLookupService
    ) {
    }

    /**
     * Look up car details by registration number.
     *
     * @param LookupCarByRegistrationRequest $request
     * @return JsonResponse
     */
    public function lookupCarByRegistration(LookupCarByRegistrationRequest $request): JsonResponse
    {
        try {
            $registrationNumber = $request->validated('registrationNumber');

            $carDetails = $this->carLookupService->lookupByRegistration($registrationNumber);

            if (!$carDetails) {
                return $this->response(
                    null,
                    'Car not found for the provided registration number',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->resourceResponse(new CarLookupResource($carDetails));
        } catch (Exception $e) {
            return $this->response(
                null,
                'An error occurred while looking up the car details',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Search cars by make and model.
     *
     * @param SearchCarsByMakeModelRequest $request
     * @return JsonResponse|CarSearchResultCollection
     */
    public function searchCarsByMakeModel(SearchCarsByMakeModelRequest $request): JsonResponse|CarSearchResultCollection
    {
        try {
            $query = $request->validated('query');

            $searchResults = $this->carLookupService->searchByMakeModel($query);

            if (empty($searchResults)) {
                return $this->response(
                    [],
                    'No cars found matching the search criteria',
                    Response::HTTP_NOT_FOUND
                );
            }

            return new CarSearchResultCollection(
                CarSearchResultResource::collection($searchResults)
            );
        } catch (Exception $e) {
            return $this->response(
                null,
                'An error occurred while searching for cars',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
