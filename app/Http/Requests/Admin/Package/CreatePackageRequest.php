<?php

namespace App\Http\Requests\Admin\Package;

use Illuminate\Foundation\Http\FormRequest;

class CreatePackageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create packages');
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:200'],
            'description' => ['nullable', 'string', 'max:2000'],
            'duration_minutes' => ['nullable', 'integer', 'min:1', 'max:1440'], // Max 24 hours
            'included_features' => ['nullable', 'array'],
            'included_features.*' => ['string', 'max:255'],
            'feature_description' => ['nullable', 'string', 'max:2000'],

            // Price points
            'standard_price' => ['nullable', 'numeric', 'min:0', 'max:99999.99'],
            'higher_price' => ['nullable', 'numeric', 'min:0', 'max:99999.99'],
            'lower_price' => ['nullable', 'numeric', 'min:0', 'max:99999.99'],
            'currency' => ['nullable', 'string', 'size:3', 'in:GBP,USD,EUR'],

            // Service group relationship
            'service_group_id' => ['required', 'integer', 'exists:service_groups,id'],

            // Visibility and status
            'is_active' => ['nullable', 'boolean'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Package name is required.',
            'name.max' => 'Package name cannot exceed 200 characters.',
            'description.max' => 'Description cannot exceed 2000 characters.',
            'duration_minutes.min' => 'Duration must be at least 1 minute.',
            'duration_minutes.max' => 'Duration cannot exceed 24 hours (1440 minutes).',
            'feature_description.max' => 'Feature description cannot exceed 2000 characters.',
            'standard_price.min' => 'Standard price must be a positive number.',
            'standard_price.max' => 'Standard price cannot exceed £99,999.99.',
            'higher_price.min' => 'Higher price must be a positive number.',
            'higher_price.max' => 'Higher price cannot exceed £99,999.99.',
            'lower_price.min' => 'Lower price must be a positive number.',
            'lower_price.max' => 'Lower price cannot exceed £99,999.99.',
            'currency.in' => 'Currency must be one of: GBP, USD, EUR.',
            'service_group_id.required' => 'Service group is required.',
            'service_group_id.exists' => 'Selected service group does not exist.',
            'sort_order.min' => 'Sort order must be a positive number.',
        ];
    }

    protected function prepareForValidation(): void
    {
        // Convert included_features from string to array if needed
        if ($this->has('included_features') && is_string($this->included_features)) {
            $features = array_filter(array_map('trim', explode("\n", $this->included_features)));
            $this->merge(['included_features' => $features]);
        }

        // Set default currency if not provided
        if (!$this->has('currency') || empty($this->currency)) {
            $this->merge(['currency' => 'GBP']);
        }
    }
}
