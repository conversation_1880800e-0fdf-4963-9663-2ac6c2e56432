<?php

namespace App\Http\Requests\Admin\Package;

use Illuminate\Foundation\Http\FormRequest;

class PackageListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('view packages');
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string', 'max:255'],
            'service_group_id' => ['nullable', 'integer', 'exists:service_groups,id'],
            'is_active' => ['nullable', 'boolean'],
            'price_min' => ['nullable', 'numeric', 'min:0'],
            'price_max' => ['nullable', 'numeric', 'min:0', 'gte:price_min'],
            'sortBy' => ['nullable', 'string', 'in:id,name,sort_order,standard_price,created_at,updated_at'],
            'orderBy' => ['nullable', 'string', 'in:asc,desc'],
            'perPage' => ['nullable', 'integer', 'min:5', 'max:100'],
        ];
    }

    public function messages(): array
    {
        return [
            'service_group_id.exists' => 'Selected service group does not exist.',
            'price_min.min' => 'Minimum price must be a positive number.',
            'price_max.min' => 'Maximum price must be a positive number.',
            'price_max.gte' => 'Maximum price must be greater than or equal to minimum price.',
        ];
    }
}
