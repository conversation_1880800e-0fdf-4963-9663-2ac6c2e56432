<?php

namespace App\Http\Requests\Admin\ServiceGroup;

use Illuminate\Foundation\Http\FormRequest;

class CreateServiceGroupRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create service groups');
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:100', 'unique:service_groups,name'],
            'description' => ['nullable', 'string', 'max:1000'],
            'is_active' => ['nullable', 'boolean'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Service group name is required.',
            'name.unique' => 'A service group with this name already exists.',
            'name.max' => 'Service group name cannot exceed 100 characters.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'sort_order.min' => 'Sort order must be a positive number.',
        ];
    }
}
