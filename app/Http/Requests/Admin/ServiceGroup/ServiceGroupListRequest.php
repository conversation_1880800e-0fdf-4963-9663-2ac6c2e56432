<?php

namespace App\Http\Requests\Admin\ServiceGroup;

use Illuminate\Foundation\Http\FormRequest;

class ServiceGroupListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('view service groups');
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string', 'max:255'],
            'is_active' => ['nullable', 'boolean'],
            'sortBy' => ['nullable', 'string', 'in:id,name,sort_order,created_at,updated_at'],
            'orderBy' => ['nullable', 'string', 'in:asc,desc'],
            'perPage' => ['nullable', 'integer', 'min:5', 'max:100'],
        ];
    }
}
