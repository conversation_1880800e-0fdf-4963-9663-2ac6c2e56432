<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CancelSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cancellationReasonId' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'cancellationReasonNotes' => [
                'nullable',
                'string',
                'max:1000',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'cancellationReasonId.integer' => 'Cancellation reason ID must be a valid integer.',
            'cancellationReasonId.min' => 'Cancellation reason ID must be at least 1.',
            'cancellationReasonNotes.string' => 'Cancellation reason notes must be a string.',
            'cancellationReasonNotes.max' => 'Cancellation reason notes cannot exceed 1000 characters.',
        ];
    }

    public function attributes(): array
    {
        return [
            'cancellationReasonId' => 'cancellation reason',
            'cancellationReasonNotes' => 'cancellation notes',
        ];
    }
}
