<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CarListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['sometimes', 'string', 'nullable', 'max:100'],
            'category' => ['sometimes', 'string', 'nullable', 'max:50'],
            'make' => ['sometimes', 'string', 'nullable', 'max:50'],
            'year' => ['sometimes', 'integer', 'nullable', 'min:1900', 'max:' . (date('Y') + 1)],
            'colour' => ['sometimes', 'string', 'nullable', 'max:50'],
            'sortBy' => [
                'sometimes',
                Rule::in([
                    'registration_number',
                    'make_and_model',
                    'make',
                    'model',
                    'colour',
                    'year',
                    'created_at',
                    'updated_at'
                ])
            ],
            'orderBy' => ['sometimes', Rule::in(['asc', 'desc'])],
            'perPage' => ['sometimes', 'integer', 'min:1', 'max:100'],
        ];
    }

    public function messages(): array
    {
        return [
            'search.max' => 'Search query must not exceed 100 characters.',
            'category.max' => 'Category filter must not exceed 50 characters.',
            'make.max' => 'Make filter must not exceed 50 characters.',
            'year.integer' => 'Year must be a valid integer.',
            'year.min' => 'Year must be 1900 or later.',
            'year.max' => 'Year cannot be more than next year.',
            'colour.max' => 'Colour filter must not exceed 50 characters.',
            'sortBy.in' => 'Invalid sort field.',
            'orderBy.in' => 'Order must be either asc or desc.',
            'perPage.integer' => 'Per page must be a valid integer.',
            'perPage.min' => 'Per page must be at least 1.',
            'perPage.max' => 'Per page cannot exceed 100.',
        ];
    }

    public function attributes(): array
    {
        return [
            'perPage' => 'items per page',
            'sortBy' => 'sort field',
            'orderBy' => 'sort order',
        ];
    }
}
