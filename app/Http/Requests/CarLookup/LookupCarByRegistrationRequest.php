<?php

namespace App\Http\Requests\CarLookup;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Request validation for car lookup by registration number.
 */
class LookupCarByRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so no authorization required
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'registrationNumber' => [
                'required',
                'string',
                'min:2',
                'max:15',
                'regex:/^[A-Za-z0-9\s]+$/', // Allow alphanumeric characters and spaces
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'registrationNumber.required' => 'Registration number is required.',
            'registrationNumber.string' => 'Registration number must be a string.',
            'registrationNumber.min' => 'Registration number must be at least 2 characters long.',
            'registrationNumber.max' => 'Registration number must not exceed 15 characters.',
            'registrationNumber.regex' => 'Registration number can only contain letters, numbers, and spaces.',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Get registration number from route parameter
        $this->merge([
            'registrationNumber' => $this->route('registrationNumber'),
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'registrationNumber' => 'registration number',
        ];
    }
}
