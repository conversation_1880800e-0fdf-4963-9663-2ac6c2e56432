<?php

namespace App\Http\Requests\CarLookup;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Request validation for car search by make and model.
 */
class SearchCarsByMakeModelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so no authorization required
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'query' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[A-Za-z0-9\s\-\.]+$/', // Allow alphanumeric, spaces, hyphens, and dots
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'query.required' => 'Search query is required.',
            'query.string' => 'Search query must be a string.',
            'query.min' => 'Search query must be at least 2 characters long.',
            'query.max' => 'Search query must not exceed 100 characters.',
            'query.regex' => 'Search query can only contain letters, numbers, spaces, hyphens, and dots.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'query' => 'search query',
        ];
    }
}
