<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'customerCarID' => [
                'required',
                'integer',
                'exists:cars,id',
                function ($attribute, $value, $fail) {
                    // Ensure the car belongs to the authenticated user
                    if (!auth()->user()->cars()->where('id', $value)->exists()) {
                        $fail('The selected car does not belong to you.');
                    }
                },
            ],
            'frequency' => [
                'required',
                'integer',
                'min:1',
                'max:52', // Maximum once per week for a year
            ],
            'cleans' => [
                'required',
                'integer',
                'min:1',
                'max:10', // Reasonable maximum
            ],
            'resourceId' => [
                'required',
                'integer',
                'min:1',
            ],
            'resourceName' => [
                'nullable',
                'string',
                'max:255',
            ],
            'category' => [
                'nullable',
                'string',
                'max:50',
            ],
            'packageGroupId' => [
                'required',
                'integer',
                'min:1',
            ],
            'customerName' => [
                'nullable',
                'string',
                'max:255',
            ],
            'paymentDetails' => [
                'nullable',
                'array',
            ],
            'paymentDetails.isPayNow' => [
                'required_with:paymentDetails',
                'boolean',
            ],
            'paymentDetails.paymentMethodId' => [
                'required_if:paymentDetails.isPayNow,true',
                'string',
                'max:255',
            ],
            'paymentDetails.paymentIntentId' => [
                'nullable',
                'string',
                'max:255',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'customerCarID.required' => 'Customer car ID is required.',
            'customerCarID.exists' => 'The selected car does not exist.',
            'frequency.required' => 'Frequency is required.',
            'frequency.integer' => 'Frequency must be a valid integer.',
            'frequency.min' => 'Frequency must be at least 1.',
            'frequency.max' => 'Frequency cannot exceed 52.',
            'cleans.required' => 'Number of cleans is required.',
            'cleans.integer' => 'Number of cleans must be a valid integer.',
            'cleans.min' => 'Number of cleans must be at least 1.',
            'cleans.max' => 'Number of cleans cannot exceed 10.',
            'resourceId.required' => 'Resource ID is required.',
            'resourceId.integer' => 'Resource ID must be a valid integer.',
            'packageGroupId.required' => 'Package group ID is required.',
            'packageGroupId.integer' => 'Package group ID must be a valid integer.',
            'paymentDetails.paymentMethodId.required_if' => 'Payment method ID is required when paying now.',
        ];
    }

    public function attributes(): array
    {
        return [
            'customerCarID' => 'customer car',
            'resourceId' => 'resource',
            'packageGroupId' => 'package group',
            'paymentDetails.isPayNow' => 'pay now option',
            'paymentDetails.paymentMethodId' => 'payment method',
        ];
    }
}
