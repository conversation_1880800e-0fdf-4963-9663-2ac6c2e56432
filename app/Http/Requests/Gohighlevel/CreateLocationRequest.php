<?php

namespace App\Http\Requests\Gohighlevel;

use Illuminate\Foundation\Http\FormRequest;

class CreateLocationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'location_id' => 'required|string|max:255',
            'private_integration_key' => 'nullable|string|max:255',
            'client_id' => 'nullable|string|max:255',
            'client_secret' => 'nullable|string|max:255',
            'base_url_authorization' => 'required|string|max:255',
            'base_url_api' => 'required|string|max:255',
        ];
    }
}
