<?php

namespace App\Http\Requests\Gohighlevel;

use Illuminate\Foundation\Http\FormRequest;

class RefreshLocationTokenRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        $this->merge([
            'location_id' => $this->route('location_id')
        ]);
    }


    public function rules(): array
    {
        return [
            'location_id' => [
                'required',
                'exists:gohighlevel_locations,id',
            ],
        ];
    }
}
