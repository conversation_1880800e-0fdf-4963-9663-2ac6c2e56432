<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PauseSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'reason' => [
                'nullable',
                'string',
                'max:500',
            ],
            'pauseUntil' => [
                'nullable',
                'date',
                'after:today',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'reason.string' => 'Pause reason must be a string.',
            'reason.max' => 'Pause reason cannot exceed 500 characters.',
            'pauseUntil.date' => 'Pause until date must be a valid date.',
            'pauseUntil.after' => 'Pause until date must be in the future.',
        ];
    }

    public function attributes(): array
    {
        return [
            'reason' => 'pause reason',
            'pauseUntil' => 'pause until date',
        ];
    }
}
