<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ResumeSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'reason' => [
                'nullable',
                'string',
                'max:500',
            ],
            'resumeDate' => [
                'nullable',
                'date',
                'after_or_equal:today',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'reason.string' => 'Resume reason must be a string.',
            'reason.max' => 'Resume reason cannot exceed 500 characters.',
            'resumeDate.date' => 'Resume date must be a valid date.',
            'resumeDate.after_or_equal' => 'Resume date cannot be in the past.',
        ];
    }

    public function attributes(): array
    {
        return [
            'reason' => 'resume reason',
            'resumeDate' => 'resume date',
        ];
    }
}
