<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCarRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $carId = $this->route('id');

        return [
            'registrationNumber' => [
                'sometimes',
                'required',
                'string',
                'min:1',
                'max:50',
                Rule::unique('cars', 'registration_number')->ignore($carId),
            ],
            'makeAndModel' => [
                'sometimes',
                'required',
                'string',
                'min:1',
                'max:150',
            ],
            'category' => [
                'nullable',
                'string',
                'max:10',
            ],
            'colour' => [
                'nullable',
                'string',
                'max:50',
            ],
            'year' => [
                'nullable',
                'string',
                'max:10',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'registrationNumber.required' => 'Registration number is required.',
            'registrationNumber.unique' => 'This registration number is already registered.',
            'makeAndModel.required' => 'Make and model is required.',
        ];
    }

    public function attributes(): array
    {
        return [
            'registrationNumber' => 'registration number',
            'makeAndModel' => 'make and model',
        ];
    }
}
