<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WebhookMonitoringRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('view webhook monitoring');
    }

    public function rules(): array
    {
        return [
            'period' => ['sometimes', 'string', 'in:today,week,month,all'],
            'sortBy' => ['sometimes', 'string', 'in:created_at,description,id'],
            'orderBy' => ['sometimes', 'string', 'in:asc,desc'],
        ];
    }
}
