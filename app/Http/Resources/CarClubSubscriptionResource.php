<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CarClubSubscriptionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'frequency' => $this->frequency,
            'cleans' => $this->cleans,
            'resourceId' => $this->resource_id,
            'resourceName' => $this->resource_name,
            'category' => $this->category,
            'packageGroupId' => $this->package_group_id,
            'customerName' => $this->customer_name,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'car' => $this->when($this->relationLoaded('car'), function () {
                return [
                    'id' => $this->car->id,
                    'registrationNumber' => $this->car->registration_number,
                    'makeAndModel' => $this->car->make_and_model,
                    'make' => $this->car->make,
                    'model' => $this->car->model,
                    'colour' => $this->car->colour,
                    'year' => $this->car->year,
                    'category' => $this->when($this->car->relationLoaded('category') && $this->car->category, function () {
                        return [
                            'id' => $this->car->category->id,
                            'name' => $this->car->category->name,
                        ];
                    }),
                ];
            }),
            'subscription' => [
                'stripeSubscriptionId' => $this->stripe_subscription_id,
                'stripeCustomerId' => $this->stripe_customer_id,
                'stripePriceId' => $this->stripe_price_id,
            ],
            'cancellation' => $this->when($this->isCancelled(), function () {
                return [
                    'reasonId' => $this->cancellation_reason_id,
                    'reasonNotes' => $this->cancellation_reason_notes,
                    'cancelledAt' => $this->cancelled_at?->toISOString(),
                ];
            }),
            'pauseResume' => [
                'pauseReason' => $this->pause_reason,
                'pauseUntil' => $this->pause_until?->toISOString(),
                'pausedAt' => $this->paused_at?->toISOString(),
                'resumeReason' => $this->resume_reason,
                'resumeDate' => $this->resume_date?->toISOString(),
                'resumedAt' => $this->resumed_at?->toISOString(),
            ],
            'dates' => [
                'createdAt' => $this->created_at?->toISOString(),
                'updatedAt' => $this->updated_at?->toISOString(),
                'expiresAt' => $this->expires_at?->toISOString(),
            ],
            'actions' => [
                'canPause' => $this->canBePaused(),
                'canResume' => $this->canBeResumed(),
                'canCancel' => $this->canBeCancelled(),
            ],
        ];
    }
}
