<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource for car lookup response.
 *
 * This resource formats the car details returned from a registration lookup.
 */
class CarLookupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'registrationNumber' => $this->resource['registrationNumber'] ?? null,
            'makeAndModel' => $this->resource['makeAndModel'] ?? null,
            'make' => $this->resource['make'] ?? null,
            'model' => $this->resource['model'] ?? null,
            'category' => $this->resource['category'] ?? null,
            'colour' => $this->resource['colour'] ?? null,
            'year' => $this->resource['year'] ?? null,
            'fuelType' => $this->resource['fuelType'] ?? null,
            'engineSize' => $this->resource['engineSize'] ?? null,
            'transmission' => $this->resource['transmission'] ?? null,
            'motExpiryDate' => $this->resource['motExpiryDate'] ?? null,
            'taxDueDate' => $this->resource['taxDueDate'] ?? null,
            'co2Emissions' => $this->resource['co2Emissions'] ?? null,
        ];

        // Include MOT history if available
        if (isset($this->resource['motHistory'])) {
            $data['motHistory'] = $this->resource['motHistory'];
        }

        return $data;
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'timestamp' => now()->toISOString(),
                'source' => 'car-lookup-service',
            ],
        ];
    }
}
