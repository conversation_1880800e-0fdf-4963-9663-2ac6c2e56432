<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CarResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'registrationNumber' => $this->registration_number,
            'makeAndModel' => $this->make_and_model,
            'make' => $this->make,
            'model' => $this->model,
            'category' => $this->when($this->category, function () {
                return [
                    'id' => $this->category->id,
                    'name' => $this->category->name,
                ];
            }),
            'colour' => $this->colour,
            'year' => $this->year,
            'fuelType' => $this->fuel_type,
            'engineSize' => $this->engine_size,
            'transmission' => $this->transmission,
            'vin' => $this->vin,
            'motExpiry' => $this->mot_expiry?->format('Y-m-d'),
            'taxExpiry' => $this->tax_expiry?->format('Y-m-d'),
            'insuranceGroup' => $this->insurance_group,
            'co2Emissions' => $this->co2_emissions,
            'createdAt' => $this->created_at?->toISOString(),
            'updatedAt' => $this->updated_at?->toISOString(),
        ];
    }
}
