<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource for car search results.
 *
 * This resource formats the car search results returned from a make/model search.
 */
class CarSearchResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'makeAndModel' => $this->resource['makeAndModel'] ?? null,
            'make' => $this->resource['make'] ?? null,
            'model' => $this->resource['model'] ?? null,
            'category' => $this->resource['category'] ?? null,
            'variants' => $this->resource['variants'] ?? [],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'timestamp' => now()->toISOString(),
                'source' => 'car-search-service',
            ],
        ];
    }
}
