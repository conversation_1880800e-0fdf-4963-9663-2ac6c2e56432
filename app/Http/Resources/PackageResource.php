<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PackageResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'durationMinutes' => $this->duration_minutes,
            'durationFormatted' => $this->duration_formatted,
            'includedFeatures' => $this->included_features,
            'featureDescription' => $this->feature_description,

            // Price points
            'standardPrice' => $this->standard_price,
            'higherPrice' => $this->higher_price,
            'lowerPrice' => $this->lower_price,
            'currency' => $this->currency,
            'mainPrice' => $this->main_price,
            'priceRange' => $this->price_range,

            // Service group relationship
            'serviceGroupId' => $this->service_group_id,
            'serviceGroup' => new ServiceGroupResource($this->whenLoaded('serviceGroup')),

            // Visibility and status
            'isActive' => $this->is_active,
            'sortOrder' => $this->sort_order,

            // Usage statistics
            'subscriptionsCount' => $this->whenLoaded('carClubSubscriptions', fn () => $this->carClubSubscriptions->count()),
            'activeSubscriptionsCount' => $this->whenLoaded('carClubSubscriptions', fn () => $this->carClubSubscriptions->where('status', 'active')->count()),

            'createdAt' => $this->created_at?->toISOString(),
            'updatedAt' => $this->updated_at?->toISOString(),
        ];
    }
}
