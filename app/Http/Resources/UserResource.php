<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin User */
class UserResource extends JsonResource
{
    /**
     * @inheritDoc
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'birth_date' => $this->birth_date,
            'initials' => $this->initials,
            'roles' => $this->roles->pluck('name'),
            'teams' => $this->teams->select('id', 'name'),
            'has_subscribed' => $this->subscribed(),
            'image' => $this->image,
            'image_thumbnail' => $this->image_thumbnail,
            'onboarded' => $this->onboarded,
        ];
    }
}
