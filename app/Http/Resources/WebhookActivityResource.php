<?php

namespace App\Http\Resources;

use App\Services\WebhookActivityService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WebhookActivityResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // Handle properties - can be Collection, array, or null
        $properties = [];
        if ($this->properties instanceof \Illuminate\Support\Collection) {
            $properties = $this->properties->toArray();
        } elseif (is_array($this->properties)) {
            $properties = $this->properties;
        }

        return [
            'id' => $this->id,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'formatted_date' => $this->created_at->format('M j, Y g:i A'),
            'time_ago' => $this->created_at->diffForHumans(),
            'properties' => $properties,
            'webhook_id' => $properties['webhook_id'] ?? null,
            'webhook_name' => $properties['webhook_name'] ?? null,
            'customer_email' => $properties['customer_email'] ?? null,
            'customer_type' => $properties['customer_type'] ?? null,
            'booking_type' => $properties['booking_type'] ?? null,
            'processing_time' => $properties['processing_time_seconds'] ?? null,
            'signature_validation' => $properties['signature_validation'] ?? null,
            'ghl_location_id' => $properties['ghl_location_id'] ?? null,
            'ghl_location_name' => $properties['ghl_location_name'] ?? null,
            'ghl_webhook_id' => $properties['ghl_webhook_id'] ?? null,
            'location_display' => $this->getLocationDisplay($properties),
            'webhook_group_id' => $this->getWebhookGroupId($properties),
            'status' => $this->getStatus(),
            'status_badge_class' => $this->getStatusBadgeClass(),
            'activity_type' => $this->getActivityType(),
            'activity_icon' => $this->getActivityIcon(),
        ];
    }

    private function getStatus(): string
    {
        return match ($this->description) {
            WebhookActivityService::LOG_WEBHOOK_RECEIVED => 'Received',
            WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED => 'Signature Validated',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_STARTED => 'Processing Started',
            WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK => 'Email Checked',
            WebhookActivityService::LOG_WEBHOOK_BOOKING_CREATED => 'Booking Created',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED => 'Completed',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED => 'Failed',
            default => 'Unknown',
        };
    }

    private function getStatusBadgeClass(): string
    {
        return match ($this->description) {
            WebhookActivityService::LOG_WEBHOOK_RECEIVED => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
            WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_STARTED => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
            WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK => 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
            WebhookActivityService::LOG_WEBHOOK_BOOKING_CREATED => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
        };
    }

    private function getActivityType(): string
    {
        return match ($this->description) {
            WebhookActivityService::LOG_WEBHOOK_RECEIVED => 'webhook',
            WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED => 'security',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_STARTED => 'processing',
            WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK => 'customer',
            WebhookActivityService::LOG_WEBHOOK_BOOKING_CREATED => 'booking',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED => 'success',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED => 'error',
            default => 'unknown',
        };
    }

    private function getActivityIcon(): string
    {
        return match ($this->description) {
            WebhookActivityService::LOG_WEBHOOK_RECEIVED => 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
            WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED => 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_STARTED => 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15',
            WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK => 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
            WebhookActivityService::LOG_WEBHOOK_BOOKING_CREATED => 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED => 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED => 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',
            default => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        };
    }

    private function getLocationDisplay(array $properties): string
    {
        $locationName = $properties['ghl_location_name'] ?? null;
        $locationId = $properties['ghl_location_id'] ?? null;

        if ($locationName) {
            return $locationName;
        } elseif ($locationId) {
            return "Location: {$locationId}";
        }

        return 'Unknown Location';
    }

    private function getWebhookGroupId(array $properties): string
    {
        // Use GoHighLevel webhook ID for grouping if available, otherwise fall back to our webhook ID
        return $properties['ghl_webhook_id'] ?? $properties['webhook_id'] ?? 'unknown';
    }
}
