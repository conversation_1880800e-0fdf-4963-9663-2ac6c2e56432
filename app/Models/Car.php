<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Car extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'registration_number',
        'make_and_model',
        'make',
        'model',
        'category_id',
        'colour',
        'year',
        'fuel_type',
        'engine_size',
        'transmission',
        'vin',
        'mot_expiry',
        'tax_expiry',
        'insurance_group',
        'co2_emissions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'mot_expiry' => 'date',
        'tax_expiry' => 'date',
        'year' => 'integer',
        'insurance_group' => 'integer',
        'co2_emissions' => 'integer',
    ];

    /**
     * Scope to search cars by registration number.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $registrationNumber
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRegistration($query, string $registrationNumber)
    {
        $normalized = strtoupper(str_replace(' ', '', $registrationNumber));
        return $query->whereRaw('UPPER(REPLACE(registration_number, \' \', \'\')) = ?', [$normalized]);
    }

    /**
     * Scope to search cars by make and model.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $searchTerm
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByMakeModel($query, string $searchTerm)
    {
        // Use LIKE for SQLite, ILIKE for PostgreSQL
        $operator = config('database.default') === 'sqlite' ? 'LIKE' : 'ILIKE';

        return $query->where(function ($q) use ($searchTerm, $operator) {
            $q->where('make_and_model', $operator, "%{$searchTerm}%")
              ->orWhere('make', $operator, "%{$searchTerm}%")
              ->orWhere('model', $operator, "%{$searchTerm}%");
        });
    }

    /**
     * Get the normalized registration number.
     *
     * @return string
     */
    public function getNormalizedRegistrationAttribute(): string
    {
        return strtoupper(str_replace(' ', '', $this->registration_number));
    }

    /**
     * Get the full car description.
     *
     * @return string
     */
    public function getFullDescriptionAttribute(): string
    {
        $parts = array_filter([
            $this->year,
            $this->make_and_model,
            $this->colour,
            $this->fuel_type
        ]);

        return implode(' ', $parts);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(CarCategory::class, 'category_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
