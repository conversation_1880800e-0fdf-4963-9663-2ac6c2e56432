<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CarClubSubscription extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'car_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'stripe_price_id',
        'status',
        'frequency',
        'cleans',
        'resource_id',
        'resource_name',
        'category',
        'package_group_id',
        'customer_name',
        'amount',
        'currency',
        'cancellation_reason_id',
        'cancellation_reason_notes',
        'pause_reason',
        'pause_until',
        'paused_at',
        'resume_reason',
        'resume_date',
        'resumed_at',
        'cancelled_at',
        'expires_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'frequency' => 'integer',
        'cleans' => 'integer',
        'resource_id' => 'integer',
        'package_group_id' => 'integer',
        'cancellation_reason_id' => 'integer',
        'pause_until' => 'datetime',
        'paused_at' => 'datetime',
        'resume_date' => 'datetime',
        'resumed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public const STATUS_ACTIVE = 'active';
    public const STATUS_PAUSED = 'paused';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_EXPIRED = 'expired';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function car(): BelongsTo
    {
        return $this->belongsTo(Car::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class, 'package_group_id');
    }

    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    public function scopePaused($query)
    {
        return $query->where('status', self::STATUS_PAUSED);
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    public function scopeForUser($query, User $user)
    {
        return $query->where('user_id', $user->id);
    }

    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isPaused(): bool
    {
        return $this->status === self::STATUS_PAUSED;
    }

    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    public function isExpired(): bool
    {
        return $this->status === self::STATUS_EXPIRED;
    }

    public function canBePaused(): bool
    {
        return $this->isActive();
    }

    public function canBeResumed(): bool
    {
        return $this->isPaused();
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_PAUSED]);
    }
}
