<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Model;

class GoHighLevelToken extends Model
{
    use HasTimestamps;

    protected $table = 'gohighlevel_tokens';

    protected $fillable = [
        'data',
        'gohighlevel_location_id'
    ];

    protected $casts = [
        'data' => 'array',
    ];

    public function getAccessToken(): ?string
    {
        return $this->data['access_token'] ?? null;
    }

    public function getRefreshToken(): ?string
    {
        return $this->data['refresh_token'] ?? null;
    }

    public function getExpiresIn(): ?int
    {
        return $this->data['expires_in'] ?? null;
    }

    public function getTokenType(): ?string
    {
        return $this->data['token_type'] ?? null;
    }

    public function getScope(): ?string
    {
        return $this->data['scope'] ?? null;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at ?? null;
    }

    public function getCompanyId(): ?string
    {
        return $this->data['companyId'] ?? null;
    }

    public function getLocationId(): ?string
    {
        return $this->data['locationId'] ?? null;
    }

    public function getUserId(): ?string
    {
        return $this->data['userId'] ?? null;
    }
}
