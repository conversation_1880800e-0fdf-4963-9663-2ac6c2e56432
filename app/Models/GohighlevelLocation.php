<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GohighlevelLocation extends Model
{
    use HasTimestamps;

    protected $table = 'gohighlevel_locations';

    protected $fillable = [
        'name',
        'location_id',
        'private_integration_key',
        'client_id',
        'client_secret',
        'base_url_authorization',
        'base_url_api',
    ];

    public function tokens(): HasMany
    {
        return $this->hasMany(GoHighLevelToken::class, 'gohighlevel_location_id', 'id');
    }

    public function hasValidToken(): bool
    {
    }
}
