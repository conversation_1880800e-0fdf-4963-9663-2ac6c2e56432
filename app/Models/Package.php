<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Package extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'duration_minutes',
        'included_features',
        'feature_description',
        'standard_price',
        'higher_price',
        'lower_price',
        'currency',
        'service_group_id',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'duration_minutes' => 'integer',
        'standard_price' => 'decimal:2',
        'higher_price' => 'decimal:2',
        'lower_price' => 'decimal:2',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'included_features' => 'array', // Cast to array for JSON storage
    ];

    public function serviceGroup(): BelongsTo
    {
        return $this->belongsTo(ServiceGroup::class);
    }

    public function carClubSubscriptions(): HasMany
    {
        return $this->hasMany(CarClubSubscription::class, 'package_group_id', 'id');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    public function scopeByServiceGroup($query, $serviceGroupId)
    {
        return $query->where('service_group_id', $serviceGroupId);
    }

    public function getDurationFormattedAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'Not specified';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    public function getMainPriceAttribute(): ?float
    {
        return $this->standard_price ?? $this->higher_price ?? $this->lower_price;
    }

    public function getPriceRangeAttribute(): string
    {
        $prices = array_filter([
            $this->lower_price,
            $this->standard_price,
            $this->higher_price
        ]);

        if (empty($prices)) {
            return 'Price on request';
        }

        $min = min($prices);
        $max = max($prices);

        if ($min === $max) {
            return "£{$min}";
        }

        return "£{$min} - £{$max}";
    }
}
