<?php

namespace App\Providers;

use App\Services\CarCategoryService;
use App\Services\CarClubSubscriptionService;
use App\Services\CarLookupService;
use App\Services\CarService;
use App\Services\PackageService;
use App\Services\ServiceGroupService;
use App\Services\Contracts\CarCategoryServiceInterface;
use App\Services\Contracts\CarClubSubscriptionServiceInterface;
use App\Services\Contracts\CarLookupServiceInterface;
use App\Services\Contracts\CarServiceInterface;
use App\Services\Contracts\PackageServiceInterface;
use App\Services\Contracts\ServiceGroupServiceInterface;
use Elastic\Elasticsearch\Client;
use Elastic\Elasticsearch\ClientBuilder;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Laravel\Pennant\Feature;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(Client::class, function () {
            return ClientBuilder::create()
                ->setHosts(config('database.connections.elasticsearch.hosts'))
                ->build();
        });

        // Bind CarLookupService interface to implementation
        $this->app->bind(CarLookupServiceInterface::class, CarLookupService::class);

        // Bind CarCategoryService interface to implementation
        $this->app->bind(CarCategoryServiceInterface::class, CarCategoryService::class);

        // Bind CarService interface to implementation
        $this->app->bind(CarServiceInterface::class, CarService::class);

        // Bind CarClubSubscriptionService interface to implementation
        $this->app->bind(CarClubSubscriptionServiceInterface::class, CarClubSubscriptionService::class);

        // Bind PackageService interface to implementation
        $this->app->bind(PackageServiceInterface::class, PackageService::class);

        // Bind ServiceGroupService interface to implementation
        $this->app->bind(ServiceGroupServiceInterface::class, ServiceGroupService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if ($this->app->environment('production') || $this->app->environment('staging')) {
            URL::forceScheme('https');
            $this->app['request']->server->set('HTTPS', 'on');
        }

        $this->features();
    }

    private function features(): void
    {
        try {
            $features = DB::select("SELECT * FROM features WHERE scope='__global'");
        } catch (QueryException $e) {
            // application in pre-initialised state, drop attempt
            return;
        }

        // define global (artificial Pennant scope) features for the current user scope
        foreach ($features as $feature) {
            Feature::define($feature->name, $feature->value === 'true');
        }
    }
}
