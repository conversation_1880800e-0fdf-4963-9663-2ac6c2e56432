<?php

namespace App\Providers;

use App\Services\FreshcarApi\FreshCarApiClient;
use App\Services\FreshcarApi\FreshcarApiRepository;
use App\Services\FreshcarApi\IFreshCarApiClient;
use App\Services\FreshcarApi\IFreshcarApiRepository;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class FreshcarapiProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(IFreshCarApiClient::class, function (Application $app) {
            $config = config('services.freshcarapi');
            return new FreshCarApiClient($config['url']);
        });

        $this->app->bind(IFreshcarApiRepository::class, function (Application $app) {
            return new FreshcarApiRepository($app->make(IFreshCarApiClient::class));
        });
    }
}
