<?php

namespace App\Providers;

use App\Services\GoHighLevel\GoHighLevelClient;
use App\Services\GoHighLevel\GoHighLevelRepository;
use App\Services\GoHighLevel\IGoHighLevelClient;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class GoHighLevelServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(IGoHighLevelClient::class, function (Application $app) {
            return new GoHighLevelClient();
        });

        $this->app->singleton(GoHighLevelRepository::class, function (Application $app) {
            return new GoHighLevelRepository($app->make(IGoHighLevelClient::class));
        });
    }

    public function boot()
    {
        require base_path('routes/gohighlevel.php');
    }
}
