<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/admin';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api/v1')
                ->group(base_path('routes/api.php'));

            // Add v3 API routes
            Route::middleware('api')
                ->prefix('api/v3')
                ->group(base_path('routes/api-v3.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            // Environment testing routes (for Cloud Run deployment verification)
            if (file_exists(base_path('routes/env-test.php'))) {
                Route::middleware('api')
                    ->group(base_path('routes/env-test.php'));
            }
        });
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(config('constants.api_rate_limit'))->by($request->user()?->id ?: $request->ip());
        });
    }
}
