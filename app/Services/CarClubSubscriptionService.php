<?php

namespace App\Services;

use App\Models\Car;
use App\Models\CarClubSubscription;
use App\Models\User;
use App\Services\Contracts\CarClubSubscriptionServiceInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Stripe\Exception\ApiErrorException;
use Stripe\Stripe;
use Stripe\Subscription as StripeSubscription;
use Exception;
use InvalidArgumentException;
use Log;

class CarClubSubscriptionService implements CarClubSubscriptionServiceInterface
{
    public function getUserSubscriptions(User $user, int $perPage = 15): LengthAwarePaginator
    {
        return $user->carClubSubscriptions()
            ->with(['car', 'car.category'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findUserSubscription(User $user, int $subscriptionId): ?CarClubSubscription
    {
        return $user->carClubSubscriptions()
            ->with(['car', 'car.category'])
            ->find($subscriptionId);
    }

    public function createSubscription(User $user, array $data): CarClubSubscription
    {
        // Validate that the car belongs to the user
        $car = $user->cars()->findOrFail($data['customerCarID']);

        // Create the subscription record
        $subscription = CarClubSubscription::create([
            'user_id' => $user->id,
            'car_id' => $car->id,
            'frequency' => $data['frequency'],
            'cleans' => $data['cleans'],
            'resource_id' => $data['resourceId'],
            'resource_name' => $data['resourceName'] ?? null,
            'category' => $data['category'] ?? null,
            'package_group_id' => $data['packageGroupId'],
            'customer_name' => $data['customerName'] ?? $user->full_name,
            'status' => CarClubSubscription::STATUS_ACTIVE,
        ]);

        // Process payment if payment details are provided
        if (isset($data['paymentDetails']) && $data['paymentDetails']['isPayNow']) {
            try {
                $paymentResult = $this->processPayment($user, $data['paymentDetails'], $data);

                $subscription->update([
                    'stripe_subscription_id' => $paymentResult['stripe_subscription_id'] ?? null,
                    'stripe_customer_id' => $paymentResult['stripe_customer_id'] ?? null,
                    'stripe_price_id' => $paymentResult['stripe_price_id'] ?? null,
                    'amount' => $paymentResult['amount'] ?? null,
                ]);
            } catch (Exception $e) {
                // If payment fails, mark subscription as failed or delete it
                $subscription->delete();
                throw $e;
            }
        }

        return $subscription->load(['car', 'car.category']);
    }

    public function cancelSubscription(User $user, int $subscriptionId, array $cancellationData = []): bool
    {
        $subscription = $this->findUserSubscription($user, $subscriptionId);

        if (!$subscription || !$subscription->canBeCancelled()) {
            return false;
        }

        // Cancel Stripe subscription if it exists
        if ($subscription->stripe_subscription_id) {
            $this->cancelStripeSubscription($subscription->stripe_subscription_id);
        }

        // Update subscription record
        $subscription->update([
            'status' => CarClubSubscription::STATUS_CANCELLED,
            'cancelled_at' => now(),
            'cancellation_reason_id' => $cancellationData['cancellationReasonId'] ?? null,
            'cancellation_reason_notes' => $cancellationData['cancellationReasonNotes'] ?? null,
        ]);

        return true;
    }

    public function pauseSubscription(User $user, int $subscriptionId, array $pauseData = []): bool
    {
        $subscription = $this->findUserSubscription($user, $subscriptionId);

        if (!$subscription || !$subscription->canBePaused()) {
            return false;
        }

        // Pause Stripe subscription if it exists
        if ($subscription->stripe_subscription_id) {
            $this->pauseStripeSubscription($subscription->stripe_subscription_id);
        }

        // Update subscription record
        $updateData = [
            'status' => CarClubSubscription::STATUS_PAUSED,
            'paused_at' => now(),
        ];

        if (isset($pauseData['reason'])) {
            $updateData['pause_reason'] = $pauseData['reason'];
        }

        if (isset($pauseData['pauseUntil'])) {
            $updateData['pause_until'] = $pauseData['pauseUntil'];
        }

        $subscription->update($updateData);

        return true;
    }

    public function resumeSubscription(User $user, int $subscriptionId, array $resumeData = []): bool
    {
        $subscription = $this->findUserSubscription($user, $subscriptionId);

        if (!$subscription || !$subscription->canBeResumed()) {
            return false;
        }

        // Resume Stripe subscription if it exists
        if ($subscription->stripe_subscription_id) {
            $this->resumeStripeSubscription($subscription->stripe_subscription_id);
        }

        // Update subscription record
        $updateData = [
            'status' => CarClubSubscription::STATUS_ACTIVE,
            'resumed_at' => now(),
        ];

        if (isset($resumeData['reason'])) {
            $updateData['resume_reason'] = $resumeData['reason'];
        }

        if (isset($resumeData['resumeDate'])) {
            $updateData['resume_date'] = $resumeData['resumeDate'];
        }

        $subscription->update($updateData);

        return true;
    }

    public function processPayment(User $user, array $paymentDetails, array $subscriptionData): array
    {
        if (!$paymentDetails['isPayNow']) {
            return [];
        }

        // Ensure user has Stripe customer ID
        if (!$user->hasStripeId()) {
            $user->createAsStripeCustomer();
        }

        $paymentMethodId = $paymentDetails['paymentMethodId'] ?? null;

        if ($paymentMethodId) {
            return $this->createStripeSubscription($user, $subscriptionData, $paymentMethodId);
        }

        throw new InvalidArgumentException('Payment method ID is required for subscription payment.');
    }

    public function createStripeSubscription(User $user, array $subscriptionData, string $paymentMethodId): array
    {
        try {
            // This would typically create a Stripe subscription
            // For now, we'll return mock data since we don't have actual Stripe price IDs
            return [
                'stripe_subscription_id' => 'sub_' . uniqid(),
                'stripe_customer_id' => $user->stripe_id,
                'stripe_price_id' => 'price_' . uniqid(),
                'amount' => 29.99, // This would come from your pricing logic
            ];
        } catch (ApiErrorException $e) {
            throw new Exception('Failed to create Stripe subscription: ' . $e->getMessage());
        }
    }

    public function cancelStripeSubscription(string $stripeSubscriptionId): bool
    {
        try {
            // Cancel the Stripe subscription
            // StripeSubscription::retrieve($stripeSubscriptionId)->cancel();
            return true;
        } catch (ApiErrorException $e) {
            Log::error('Failed to cancel Stripe subscription: ' . $e->getMessage());
            return false;
        }
    }

    public function pauseStripeSubscription(string $stripeSubscriptionId): bool
    {
        try {
            // Pause the Stripe subscription
            // StripeSubscription::update($stripeSubscriptionId, ['pause_collection' => ['behavior' => 'void']]);
            return true;
        } catch (ApiErrorException $e) {
            Log::error('Failed to pause Stripe subscription: ' . $e->getMessage());
            return false;
        }
    }

    public function resumeStripeSubscription(string $stripeSubscriptionId): bool
    {
        try {
            // Resume the Stripe subscription
            // StripeSubscription::update($stripeSubscriptionId, ['pause_collection' => null]);
            return true;
        } catch (ApiErrorException $e) {
            Log::error('Failed to resume Stripe subscription: ' . $e->getMessage());
            return false;
        }
    }
}
