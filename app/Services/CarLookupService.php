<?php

namespace App\Services;

use App\Models\Car;
use App\Services\Contracts\CarLookupServiceInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * CarLookupService handles car lookup operations.
 *
 * This service provides functionality for:
 * - Looking up car details by registration number
 * - Searching cars by make and model
 */
class CarLookupService implements CarLookupServiceInterface
{
    /**
     * @var DvlaMotHistoryService
     */
    protected $motHistoryService;

    /**
     * Constructor
     *
     * @param DvlaMotHistoryService $motHistoryService
     */
    public function __construct(DvlaMotHistoryService $motHistoryService)
    {
        $this->motHistoryService = $motHistoryService;
    }

    /**
     * Look up car details by registration number.
     *
     * @param string $registrationNumber The car registration number
     * @return array|null Car details or null if not found
     */
    public function lookupByRegistration(string $registrationNumber): ?array
    {
        try {
            // Normalize registration number (remove spaces, convert to uppercase)
            $normalizedRegistration = $this->normalizeRegistrationNumber($registrationNumber);

            $response = Http::withHeaders([
                'x-api-key' => config('services.dvla.api_key'),
                'Content-Type' => 'application/json',
            ])->post(config('services.dvla.api_url'), [
                'registrationNumber' => $normalizedRegistration
            ]);

            if (!$response->successful()) {
                Log::error("DVLA API error for registration {$registrationNumber}: " . $response->body());

                if ($response->status() === 404) {
                    return null;
                }

                return null;
            }

            $dvlaData = $response->json();

            $carData = $this->transformDvlaData($dvlaData);

            $motHistory = $this->motHistoryService->getMotHistory($normalizedRegistration);
            if ($motHistory) {
                if (
                    isset($motHistory['make']) && isset($carData['make'])
                    && strtolower($motHistory['make']) === strtolower($carData['make'])
                ) {
                    if (
                        (!isset($carData['model']) || empty($carData['model']))
                        && isset($motHistory['model']) && !empty($motHistory['model'])
                    ) {
                        $carData['model'] = $motHistory['model'];

                        // Update makeAndModel to include the newly added model
                        $carData['makeAndModel'] = $carData['make'] . ' ' . $carData['model'];
                    }
                }

                // Add MOT history data to car data
                // $carData['motHistory'] = $this->transformMotHistoryData($motHistory);
            }

            return $carData;
        } catch (Exception $e) {
            Log::error("Error looking up car by registration {$registrationNumber}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Search cars by make and model.
     *
     * @param string $query Search query for make and model
     * @return array Array of matching cars
     */
    public function searchByMakeModel(string $query): array
    {
        try {
            $normalizedQuery = trim(strtolower($query));

            if (strlen($normalizedQuery) < 2) {
                return [];
            }

            $searchResults = $this->getMockCarSearchResults($normalizedQuery);

            return $searchResults;
        } catch (Exception $e) {
            Log::error("Error searching cars by make/model with query {$query}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Normalize registration number for consistent lookup.
     *
     * @param string $registrationNumber
     * @return string
     */
    private function normalizeRegistrationNumber(string $registrationNumber): string
    {
        return strtoupper(str_replace(' ', '', trim($registrationNumber)));
    }

    /**
     * Mock implementation for car search by make/model.
     * In a real application, this would be replaced with database queries or external API calls.
     *
     * @param string $query
     * @return array
     */
    private function getMockCarSearchResults(string $query): array
    {
        // Mock data for demonstration
        $allCars = [
            [
                'makeAndModel' => 'BMW 3 Series',
                'make' => 'BMW',
                'model' => '3 Series',
                'category' => 'Medium',
                'variants' => ['320i', '330i', '335i']
            ],
            [
                'makeAndModel' => 'BMW 5 Series',
                'make' => 'BMW',
                'model' => '5 Series',
                'category' => 'Large',
                'variants' => ['520i', '530i', '540i']
            ],
            [
                'makeAndModel' => 'Audi A4',
                'make' => 'Audi',
                'model' => 'A4',
                'category' => 'Medium',
                'variants' => ['A4 Avant', 'A4 Saloon']
            ],
            [
                'makeAndModel' => 'Audi A6',
                'make' => 'Audi',
                'model' => 'A6',
                'category' => 'Large',
                'variants' => ['A6 Avant', 'A6 Saloon']
            ],
            [
                'makeAndModel' => 'Ford Focus',
                'make' => 'Ford',
                'model' => 'Focus',
                'category' => 'Small',
                'variants' => ['Focus Hatchback', 'Focus Estate']
            ],
            [
                'makeAndModel' => 'Ford Fiesta',
                'make' => 'Ford',
                'model' => 'Fiesta',
                'category' => 'Small',
                'variants' => ['Fiesta Hatchback']
            ]
        ];

        // Filter cars based on query
        return array_filter($allCars, function ($car) use ($query) {
            $searchText = strtolower($car['makeAndModel'] . ' ' . $car['make'] . ' ' . $car['model']);
            return strpos($searchText, $query) !== false;
        });
    }

    /**
     * Transform DVLA API data to our format.
     *
     * @param array $dvlaData
     * @return array
     */
    private function transformDvlaData(array $dvlaData): array
    {
        return [
            'registrationNumber' => $dvlaData['registrationNumber'] ?? null,
            'makeAndModel' => ($dvlaData['make'] ?? '') . ' ' . ($dvlaData['model'] ?? ''),
            'make' => $dvlaData['make'] ?? null,
            'model' => $dvlaData['model'] ?? null,
            // 'category' => $this->mapVehicleCategory($dvlaData['wheelplan'] ?? ''),
            'colour' => $dvlaData['colour'] ?? null,
            'year' => $dvlaData['yearOfManufacture'] ?? null,
            'fuelType' => $dvlaData['fuelType'] ?? null,
            'engineSize' => $dvlaData['engineCapacity'] ?? null,
            // 'engineSize' => isset($dvlaData['engineCapacity']) ? ($dvlaData['engineCapacity'] / 1000) . 'L' : null,
            'transmission' => $dvlaData['transmission'] ?? null,
            'motExpiryDate' => $dvlaData['motExpiryDate'] ?? null,
            'taxDueDate' => $dvlaData['taxDueDate'] ?? null,
            'co2Emissions' => $dvlaData['co2Emissions'] ?? null,
        ];
    }

    /**
     * Map DVLA wheelplan to our vehicle category.
     *
     * @param string $wheelplan
     * @return string
     */
    private function mapVehicleCategory(string $wheelplan): string
    {
        $categoryMap = [
            '2 WHEEL' => 'Motorcycle',
            '2 AXLE RIGID BODY' => 'Small',
            '3 AXLE RIGID BODY' => 'Medium',
            '4 OR MORE AXLE RIGID BODY' => 'Large',
            // Add more mappings as needed
        ];

        return $categoryMap[$wheelplan] ?? 'Unknown';
    }

    /**
     * Transform MOT history data to our format.
     *
     * @param array $motHistoryData
     * @return array
     */
    private function transformMotHistoryData(array $motHistoryData): array
    {
        $transformedData = [
            'registration' => $motHistoryData['registration'] ?? null,
            'make' => $motHistoryData['make'] ?? null,
            'model' => $motHistoryData['model'] ?? null,
            'firstUsedDate' => $motHistoryData['firstUsedDate'] ?? null,
            'fuelType' => $motHistoryData['fuelType'] ?? null,
            'primaryColour' => $motHistoryData['primaryColour'] ?? null,
            'registrationDate' => $motHistoryData['registrationDate'] ?? null,
            'manufactureDate' => $motHistoryData['manufactureDate'] ?? null,
            'engineSize' => $motHistoryData['engineSize'] ?? null,
            'hasOutstandingRecall' => $motHistoryData['hasOutstandingRecall'] ?? null,
            'tests' => [],
        ];

        if (isset($motHistoryData['motTests']) && is_array($motHistoryData['motTests'])) {
            foreach ($motHistoryData['motTests'] as $test) {
                $transformedTest = [
                    'testDate' => $test['completedDate'] ?? null,
                    'testResult' => $test['testResult'] ?? null,
                    'expiryDate' => $test['expiryDate'] ?? null,
                    'odometerValue' => $test['odometerValue'] ?? null,
                    'odometerUnit' => $test['odometerUnit'] ?? null,
                    'motTestNumber' => $test['motTestNumber'] ?? null,
                ];

                // Add reasons for failure if present
                if (isset($test['rfrAndComments']) && is_array($test['rfrAndComments'])) {
                    $transformedTest['failures'] = array_map(function ($item) {
                        return [
                            'type' => $item['type'] ?? null,
                            'text' => $item['text'] ?? null,
                            'dangerous' => $item['dangerous'] ?? false,
                        ];
                    }, $test['rfrAndComments']);
                }

                $transformedData['tests'][] = $transformedTest;
            }
        }

        return $transformedData;
    }
}
