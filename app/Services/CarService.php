<?php

namespace App\Services;

use App\Models\Car;
use App\Models\CarCategory;
use App\Models\User;
use App\Services\Contracts\CarServiceInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CarService implements CarServiceInterface
{
    public function getUserCars(User $user, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $user->cars()->with('category');

        $this->applyFilters($query, $filters);

        return $query->paginate($perPage)->withQueryString();
    }

    public function findUserCar(User $user, int $carId): ?Car
    {
        return $user->cars()->with('category')->find($carId);
    }

    public function createCar(User $user, array $data): Car
    {
        $carData = [
            'user_id' => $user->id,
            'registration_number' => $data['registrationNumber'],
            'make_and_model' => $data['makeAndModel'],
            'colour' => $data['colour'] ?? null,
            'year' => $data['year'] ?? null,
        ];

        if (isset($data['category'])) {
            $category = CarCategory::where('name', $data['category'])->first();
            if ($category) {
                $carData['category_id'] = $category->id;
            }
        }

        $makeAndModel = explode(' ', $data['makeAndModel'], 2);
        $carData['make'] = $makeAndModel[0] ?? '';
        $carData['model'] = $makeAndModel[1] ?? '';

        return Car::create($carData);
    }

    public function updateCar(User $user, int $carId, array $data): ?Car
    {
        $car = $this->findUserCar($user, $carId);

        if (!$car) {
            return null;
        }

        $updateData = [];

        if (isset($data['registrationNumber'])) {
            $updateData['registration_number'] = $data['registrationNumber'];
        }

        if (isset($data['makeAndModel'])) {
            $updateData['make_and_model'] = $data['makeAndModel'];
            $makeAndModel = explode(' ', $data['makeAndModel'], 2);
            $updateData['make'] = $makeAndModel[0] ?? '';
            $updateData['model'] = $makeAndModel[1] ?? '';
        }

        if (isset($data['colour'])) {
            $updateData['colour'] = $data['colour'];
        }

        if (isset($data['year'])) {
            $updateData['year'] = $data['year'];
        }

        if (isset($data['category'])) {
            $category = CarCategory::where('name', $data['category'])->first();
            if ($category) {
                $updateData['category_id'] = $category->id;
            }
        }

        $car->update($updateData);

        return $car->fresh(['category']);
    }

    public function deleteCar(User $user, int $carId): bool
    {
        $car = $this->findUserCar($user, $carId);

        if (!$car) {
            return false;
        }

        return $car->delete();
    }

    private function applyFilters(Builder|HasMany $query, array $filters): void
    {
        // Use LIKE for SQLite, ILIKE for PostgreSQL
        $operator = config('database.default') === 'sqlite' ? 'LIKE' : 'ILIKE';

        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search, $operator) {
                $q->where('registration_number', $operator, "%{$search}%")
                  ->orWhere('make_and_model', $operator, "%{$search}%")
                  ->orWhere('make', $operator, "%{$search}%")
                  ->orWhere('model', $operator, "%{$search}%")
                  ->orWhere('colour', $operator, "%{$search}%");
            });
        }

        if (!empty($filters['category'])) {
            $query->whereHas('category', function ($q) use ($filters, $operator) {
                $q->where('name', $operator, "%{$filters['category']}%");
            });
        }

        if (!empty($filters['make'])) {
            $query->where('make', $operator, "%{$filters['make']}%");
        }

        if (!empty($filters['year'])) {
            $query->where('year', $filters['year']);
        }

        if (!empty($filters['colour'])) {
            $query->where('colour', $operator, "%{$filters['colour']}%");
        }

        if (!empty($filters['sortBy'])) {
            $sortBy = $filters['sortBy'];
            $orderBy = $filters['orderBy'] ?? 'asc';

            $allowedSortFields = [
                'registration_number', 'make_and_model', 'make', 'model',
                'colour', 'year', 'created_at', 'updated_at'
            ];

            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $orderBy);
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }
    }
}
