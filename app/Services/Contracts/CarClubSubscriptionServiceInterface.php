<?php

namespace App\Services\Contracts;

use App\Models\CarClubSubscription;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface CarClubSubscriptionServiceInterface
{
    /**
     * Get all subscriptions for a user with pagination.
     */
    public function getUserSubscriptions(User $user, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find a specific subscription for a user.
     */
    public function findUserSubscription(User $user, int $subscriptionId): ?CarClubSubscription;

    /**
     * Create a new car club subscription.
     */
    public function createSubscription(User $user, array $data): CarClubSubscription;

    /**
     * Cancel a subscription.
     */
    public function cancelSubscription(User $user, int $subscriptionId, array $cancellationData = []): bool;

    /**
     * Pause a subscription.
     */
    public function pauseSubscription(User $user, int $subscriptionId, array $pauseData = []): bool;

    /**
     * Resume a paused subscription.
     */
    public function resumeSubscription(User $user, int $subscriptionId, array $resumeData = []): bool;

    /**
     * Process payment for a subscription.
     */
    public function processPayment(User $user, array $paymentDetails, array $subscriptionData): array;

    /**
     * Create Stripe subscription.
     */
    public function createStripeSubscription(User $user, array $subscriptionData, string $paymentMethodId): array;

    /**
     * Cancel Stripe subscription.
     */
    public function cancelStripeSubscription(string $stripeSubscriptionId): bool;

    /**
     * Pause Stripe subscription.
     */
    public function pauseStripeSubscription(string $stripeSubscriptionId): bool;

    /**
     * Resume Stripe subscription.
     */
    public function resumeStripeSubscription(string $stripeSubscriptionId): bool;
}
