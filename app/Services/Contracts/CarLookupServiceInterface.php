<?php

namespace App\Services\Contracts;

/**
 * Interface for car lookup operations.
 *
 * This interface defines the contract for car lookup services,
 * providing methods for registration lookup and make/model search.
 */
interface CarLookupServiceInterface
{
    /**
     * Look up car details by registration number.
     *
     * @param string $registrationNumber The car registration number
     * @return array|null Car details or null if not found
     */
    public function lookupByRegistration(string $registrationNumber): ?array;

    /**
     * Search cars by make and model.
     *
     * @param string $query Search query for make and model
     * @return array Array of matching cars
     */
    public function searchByMakeModel(string $query): array;
}
