<?php

namespace App\Services\Contracts;

use App\Models\Car;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface CarServiceInterface
{
    public function getUserCars(User $user, array $filters = [], int $perPage = 15): LengthAwarePaginator;

    public function findUserCar(User $user, int $carId): ?Car;

    public function createCar(User $user, array $data): Car;

    public function updateCar(User $user, int $carId, array $data): ?Car;

    public function deleteCar(User $user, int $carId): bool;
}
