<?php

namespace App\Services\Contracts;

use App\Models\Package;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface PackageServiceInterface
{
    public function getAllPackages(array $filters = [], int $perPage = 15): LengthAwarePaginator;

    public function getAllActivePackages(): Collection;

    public function getPackagesByServiceGroup(int $serviceGroupId, bool $activeOnly = true): Collection;

    public function findPackage(int $id): ?Package;

    public function createPackage(array $data): Package;

    public function updatePackage(Package $package, array $data): Package;

    public function deletePackage(Package $package): bool;

    public function toggleActiveStatus(Package $package): Package;

    public function duplicatePackage(Package $package, array $overrides = []): Package;
}
