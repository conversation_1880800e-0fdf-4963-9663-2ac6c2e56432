<?php

namespace App\Services\Contracts;

use App\Models\ServiceGroup;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface ServiceGroupServiceInterface
{
    public function getAllServiceGroups(array $filters = [], int $perPage = 15): LengthAwarePaginator;

    public function getAllActiveServiceGroups(): Collection;

    public function findServiceGroup(int $id): ?ServiceGroup;

    public function createServiceGroup(array $data): ServiceGroup;

    public function updateServiceGroup(ServiceGroup $serviceGroup, array $data): ServiceGroup;

    public function deleteServiceGroup(ServiceGroup $serviceGroup): bool;

    public function toggleActiveStatus(ServiceGroup $serviceGroup): ServiceGroup;
}
