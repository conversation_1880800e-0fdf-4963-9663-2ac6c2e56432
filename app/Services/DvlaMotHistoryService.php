<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class DvlaMotHistoryService
{
    /**
     * Cache key for the access token
     */
    private const TOKEN_CACHE_KEY = 'dvla_mot_history_access_token';

    /**
     * Token expiry buffer in seconds (5 minutes)
     */
    private const TOKEN_EXPIRY_BUFFER = 300;

    /**
     * Get MOT history for a vehicle by registration number
     *
     * @param string $registrationNumber
     * @return array|null
     */
    public function getMotHistory(string $registrationNumber): ?array
    {
        try {
            $token = $this->getAccessToken();

            if (!$token) {
                Log::error('Failed to obtain MOT History API access token');
                return null;
            }

            $apiUrl = config('services.dvla.mot_history.api_url') . '/v1/trade/vehicles/registration/' . $registrationNumber;
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'x-api-key' => config('services.dvla.mot_history.api_key'),
                'Content-Type' => 'application/json',
            ])->get($apiUrl);

            if (!$response->successful()) {
                Log::error("MOT History API error for registration {$registrationNumber}: " . $response->status() . " - " . $response->body());
                return null;
            }

            $motData = $response->json();

            return $motData;
        } catch (Exception $e) {
            Log::error("Error getting MOT history for {$registrationNumber}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get an access token for the MOT History API
     *
     * @return string|null
     */
    private function getAccessToken(): ?string
    {
        // Check if we have a cached token
        if (Cache::has(self::TOKEN_CACHE_KEY)) {
            return Cache::get(self::TOKEN_CACHE_KEY);
        }

        try {
            // Important: These credentials should be obtained from the MOT History API team
            // and may need to be refreshed periodically
            $clientId = config('services.dvla.mot_history.client_id');
            $clientSecret = config('services.dvla.mot_history.client_secret');
            $scope = config('services.dvla.mot_history.scope');
            $tokenUrl = config('services.dvla.mot_history.token_url');

            $response = Http::asForm()->post($tokenUrl, [
                'grant_type' => 'client_credentials',
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'scope' => $scope,
            ]);

            if (!$response->successful()) {
                Log::error('Failed to obtain MOT History API access token: ' . $response->body());
                return null;
            }

            $tokenData = $response->json();
            $token = $tokenData['access_token'];
            $expiresIn = $tokenData['expires_in'] ?? 3600;

            // Cache the token with a buffer to ensure we refresh before it expires
            Cache::put(self::TOKEN_CACHE_KEY, $token, $expiresIn - self::TOKEN_EXPIRY_BUFFER);

            return $token;
        } catch (Exception $e) {
            Log::error('Error obtaining MOT History API access token: ' . $e->getMessage());
            return null;
        }
    }
}
