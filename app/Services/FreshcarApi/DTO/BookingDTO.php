<?php

namespace App\Services\FreshcarApi\DTO;

use App\Services\FreshcarApi\Enums\Currency;
use DateTime;

class BookingDTO
{
    public function __construct(
        private int $bookingId,
        private DateTime $requestedDate,
        private DateTime $confirmedDate,
        private int $addressId,
        private string $AdditionalComments,
        private string $notes,
        private string $preferredTime,
        private int $bookingStatusId,
        private int $totalCost,
        private string $enquiryStatus,
        private int $resourceId,
        private string $resourceName,
        private string $timeOfDay,
        private string $bookingReferenceNumber,
        private string $bookingHubDurationMinutes,
        private ?int $overridePrice,
        private array $bookingCustomerCarsDTO,
        private bool $isPrepaid,
        private bool $isRefunded,
        private ?int $customerCarClubPackageId,
        private Currency $currency,
    ) {
    }
}
