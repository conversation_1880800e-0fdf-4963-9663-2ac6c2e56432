<?php

namespace App\Services\FreshcarApi;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class FreshCarApiClient implements IFreshCarApiClient
{
    public function __construct(
        private string $baseUrl,
        private ?string $token = null
    ) {
    }

    private function getDefaultAuthorizationHeaders(): array
    {
        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ];
        if ($this->token) {
            $headers['Authorization'] = 'Bearer ' . $this->token;
        }

        return $headers;
    }

    private function log(string $prefix, array $context): void
    {
        Log::channel('freshcar')->info($prefix, $context);
    }

    public function emailAvailable(string $email): bool
    {
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($this->baseUrl . '/api/Account/EmailAvailable', [
                    'email' => $email
                ]);
            $this->log('freshcar:/api/Account/EmailAvailable:response', [
                'url' => $this->baseUrl . '/api/Account/EmailAvailable',
                'email' => $email,
                'response' => $response->json(),
                'status' => $response->status()
            ]);
            $response->throw();
            $data = $response->json();
            // The API returns payload: true if email is available, false if not available
            return $data['payload'] ?? false;
        } catch (Throwable $t) {
            Log::error('freshcar:/api/Account/EmailAvailable:error', [
                'url' => $this->baseUrl . '/api/Account/EmailAvailable',
                'email' => $email,
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function newCustomerBooking(array $data): ?array
    {
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($this->baseUrl . '/api/newcustomerbookings', $data);
            $this->log('freshcar:new-customer-booking:response', [
                'url' => $this->baseUrl . '/api/newcustomerbookings',
                'status' => $response->status(),
                'data' => $data,
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error('freshcar:new-customer-booking:error', [
                'url' => $this->baseUrl . '/api/newcustomerbookings',
                'data' => $data,
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function customerBooking(array $data): ?array
    {
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($this->baseUrl . '/api/bookings', $data);
            $this->log('freshcar:previous-customer-booking:response', [
                'url' => $this->baseUrl . '/api/bookings',
                'status' => $response->status(),
                'data' => $data,
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error('freshcar:previous-customer-booking:error', [
                'url' => $this->baseUrl . '/api/bookings',
                'data' => $data,
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function authenticateAsUser(string $user, string $password): ?bool
    {
        if ($this->token) {
            return true;
        }

        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($this->baseUrl . '/api/Account/Login', [
                    'email' => $user,
                    'password' => $password
                ]);
            $this->log('freshcar:user-authenticate:response', [
                'url' => $this->baseUrl . '/api/Account/Login',
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();

            $this->token = $response->json()['payload']['token'];

            return true;
        } catch (Throwable $t) {
            Log::error('freshcar:user-authenticate:error', [
                'url' => $this->baseUrl . '/api/Account/Login',
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function logout(): void
    {
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($this->baseUrl . '/api/Account/Logout');
            $this->log('freshcar:user-logout:response', [
                'url' => $this->baseUrl . '/api/Account/Logout',
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();
        } catch (Throwable $t) {
            Log::error('freshcar:user-logout:error', [
                'url' => $this->baseUrl . '/api/Account/Logout',
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }

        $this->token = null;
    }

    public function refreshToken(string $user, string $token): ?array
    {
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($this->baseUrl . '/api/Account/RefreshToken', [
                    'email' => $user,
                    'jwtToken' => $token
                ]);
            $this->log('freshcar:user-refresh-token:response', [
                'url' => $this->baseUrl . '/api/Account/RefreshToken',
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error('freshcar:user-refresh-token:error', [
                'url' => $this->baseUrl . '/api/Account/RefreshToken',
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function adminGetAllValeters(): ?array
    {
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->get($this->baseUrl . '/api/admin/valeters/get-all-valeters');
            $this->log('freshcar:admin-get-all-valeters:response', [
                'url' => $this->baseUrl . '/api/admin/valeters/get-all-valeters',
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error('freshcar:admin-get-all-valeters:error', [
                'url' => $this->baseUrl . '/api/admin/valeters/get-all-valeters',
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function adminGetCustomers(string $searchTerm, int $currentPage = 0, int $pageSize = 100): ?array
    {
        $request = [
            'url' => $this->baseUrl . '/api/admin/customers',
            'query' => [
                'searchTerm' => $searchTerm,
                'pageNumber' => $currentPage,
                'pageSize' => $pageSize
            ]
        ];
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->get($request['url'], $request['query']);

            $this->log('freshcar:admin-get-customers:response', [
                'url' => $request['url'],
                'query' => $request['query'],
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();
            $jsonDecoded = $response->json();
            return $response->status() == Response::HTTP_OK || $jsonDecoded['totalCount'] > 0 ? $jsonDecoded : null;
        } catch (Throwable $t) {
            Log::error('freshcar:admin-get-customers:error', [
                'url' => $request['url'],
                'query' => $request['query'],
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function adminGetCustomer(int $customerId): ?array
    {
        $request = [
            'url' => $this->baseUrl . "/api/admin/customers/$customerId",
        ];
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->get($request['url']);

            $this->log("freshcar:admin-get-customers-$customerId:response", [
                'url' => $request['url'],
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error("freshcar:admin-get-customers-$customerId:error", [
                'url' => $request['url'],
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function adminGetCustomerCarClub(int $customerId): ?array
    {
        $request = [
            'url' => $this->baseUrl . "/api/admin/customercarclub/$customerId",
        ];
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->get($request['url']);

            $this->log("freshcar:admin-get-customercarclub-$customerId:response", [
                'url' => $request['url'],
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error("freshcar:admin-get-customercarclub-$customerId:error", [
                'url' => $request['url'],
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function adminAddCustomerCars(int $customerId, array $data): ?array
    {
        $request = [
            'url' => $this->baseUrl . "/api/admin/customers/$customerId/cars",
        ];

        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($request['url'], $data);
            $this->log('freshcar:add-customer-cars:response', [
                'url' => $request['url'],
                'status' => $response->status(),
                'data' => $data,
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error('freshcar:add-customer-cars:error', [
                'url' => $request['url'],
                'data' => $data,
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function adminAddCustomerAddresses(int $customerId, array $data): ?array
    {
        $request = [
            'url' => $this->baseUrl . "/api/admin/customers/$customerId/addresses",
        ];

        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($request['url'], $data);
            $this->log('freshcar:add-customer-addresses:response', [
                'url' => $request['url'],
                'status' => $response->status(),
                'data' => $data,
                'response' => $response->json()
            ]);
            $response->throw();
            return $response->json();
        } catch (Throwable $t) {
            Log::error('freshcar:add-customer-addresses:error', [
                'url' => $request['url'],
                'data' => $data,
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function adminCustomerLogin(int $customerId): ?array
    {
        $request = [
            'url' => $this->baseUrl . "/api/admin/customers/login/$customerId",
        ];
        try {
            $response = Http::withHeaders($this->getDefaultAuthorizationHeaders())
                ->post($request['url']);

            $this->log("freshcar:admin-get-customer-login-$customerId:response", [
                'url' => $request['url'],
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            $response->throw();
            $json = $response->json();
            if (!isset($json['payload']['token'])) {
                throw new Exception(sprintf('token for %d not received', $customerId));
            }
            $this->logout();
            $this->token = $json['payload']['token'];
            return $response->json();
        } catch (Throwable $t) {
            Log::error("freshcar:admin-get-customer-login-$customerId:error", [
                'url' => $request['url'],
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }
}
