<?php

namespace App\Services\FreshcarApi;

interface IFreshCarApiClient
{
    public function emailAvailable(string $email): bool;

    public function newCustomerBooking(array $data): ?array;

    public function customerBooking(array $data): ?array;

    public function authenticateAsUser(string $user, string $password): ?bool;

    public function refreshToken(string $user, string $token): ?array;

    public function adminGetAllValeters(): ?array;

    public function adminGetCustomers(string $searchTerm, int $currentPage = 0, int $pageSize = 100): ?array;

    public function adminGetCustomer(int $customerId): ?array;

    public function adminGetCustomerCarClub(int $customerId): ?array;

    public function adminCustomerLogin(int $customerId): ?array;

    public function adminAddCustomerCars(int $customerId, array $data): ?array;

    public function adminAddCustomerAddresses(int $customerId, array $data): ?array;

    public function logout(): void;
}
