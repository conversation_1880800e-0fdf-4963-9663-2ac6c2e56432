<?php

namespace App\Services\FreshcarApi;

interface IFreshcarApiRepository
{
    public function emailAvailable(string $email): ?bool;

    public function newCustomerBooking(array $webhookData): ?array;

    public function previousCustomerBooking(array $webhookData): ?array;

    public function transformGoHighLevelData(array $webhookData): array;

    public function generateTemporaryPassword(): string;
}
