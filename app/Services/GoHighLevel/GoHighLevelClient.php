<?php

namespace App\Services\GoHighLevel;

use Illuminate\Support\Facades\Http;
use InvalidArgumentException;

class GoHighLevelClient implements IGoHighLevelClient
{
    private const GRANT_TYPE_AUTHORIZATION_CODE = 'authorization_code';

    private const GRANT_TYPE_REFRESH_TOKEN = 'refresh_token';

    private function getDefaultAuthorizationHeaders(string $accessToken): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Authorization' => "Bearer $accessToken",
            'Version' => '2021-07-28',
        ];
    }

    private function getPrivateIntegrationHeaders(string $privateIntegrationKey): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer $privateIntegrationKey",
            'Version' => '2021-07-28',
        ];
    }

    public function getPrivateIntegrationAuthorizationUrl(string $baseUrl, string $privateIntegrationKey): string
    {
        if (empty($baseUrl) || empty($privateIntegrationKey)) {
            throw new InvalidArgumentException("Base URL and private integration key are required for private integration authorization URL");
        }

        return url()->query($baseUrl, [
            'private_integration_key' => $privateIntegrationKey,
        ]);
    }

    public function getAuthorizationUrl(string $baseUrlAuth, string $clientId, string $redirectUri): string
    {
        return url()->query($baseUrlAuth, [
            'response_type' => 'code',
            'redirect_uri' => $redirectUri,
            'client_id' => $clientId,
            'scope' => 'opportunities.readonly contacts.readonly locations.readonly'
        ]);
    }

    public function getTokensByCode(string $baseUrl, string $clientId, string $clientSecret, string $code): ?array
    {
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()
            ->post($baseUrl . '/oauth/token', [
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'code' => $code,
                'grant_type' => self::GRANT_TYPE_AUTHORIZATION_CODE,
            ]);
        $response->throw();

        return $response->json();
    }

    public function refreshToken(string $baseUrl, string $clientId, string $clientSecret, string $refreshToken): ?array
    {
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()
            ->post($baseUrl . '/oauth/token', [
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'refresh_token' => $refreshToken,
                'grant_type' => self::GRANT_TYPE_REFRESH_TOKEN,
            ]);
        $response->throw();

        return $response->json();
    }

    public function getPipelines(string $baseUrl, string $accessToken, string $locationId): ?array
    {
        $response = Http::withHeaders($this->getDefaultAuthorizationHeaders($accessToken))
            ->get($baseUrl . '/opportunities/pipelines', [
                'locationId' => $locationId
            ]);
        $response->throw();

        return $response->json();
    }

    public function getOpportunity(string $baseUrl, string $accessToken, string $opportunityId): ?array
    {
        $response = Http::withHeaders($this->getDefaultAuthorizationHeaders($accessToken))
            ->get("$baseUrl/opportunities/$opportunityId");

        $response->throw();

        return $response->json();
    }

    public function getContact(string $baseUrl, string $accessToken, string $contactId): ?array
    {
        $response = Http::withHeaders($this->getDefaultAuthorizationHeaders($accessToken))
            ->get("$baseUrl/contacts/$contactId");
        $response->throw();

        return $response->json();
    }

    public function getSubAccount(string $baseUrl, string $accessToken, string $locationId): ?array
    {
        $response = Http::withHeaders($this->getDefaultAuthorizationHeaders($accessToken))
            ->get("$baseUrl/locations/$locationId");
        $response->throw();

        return $response->json();
    }



    public function getPIPipelines(string $baseUrl, string $privateIntegrationKey, string $locationId): ?array
    {
        $response = Http::withHeaders($this->getPrivateIntegrationHeaders($privateIntegrationKey))
            ->get($baseUrl . '/opportunities/pipelines', [
                'locationId' => $locationId
            ]);
        $response->throw();

        return $response->json();
    }

    public function getPIOpportunity(string $baseUrl, string $privateIntegrationKey, string $opportunityId): ?array
    {
        $response = Http::withHeaders($this->getPrivateIntegrationHeaders($privateIntegrationKey))
            ->get("$baseUrl/opportunities/$opportunityId");

        $response->throw();

        return $response->json();
    }

    public function getPIContact(string $baseUrl, string $privateIntegrationKey, string $contactId): ?array
    {
        $response = Http::withHeaders($this->getPrivateIntegrationHeaders($privateIntegrationKey))
            ->get("$baseUrl/contacts/$contactId");
        $response->throw();

        return $response->json();
    }

    public function getPISubAccount(string $baseUrl, string $privateIntegrationKey, string $locationId): ?array
    {
        $response = Http::withHeaders($this->getPrivateIntegrationHeaders($privateIntegrationKey))
            ->get("$baseUrl/locations/$locationId");
        $response->throw();

        return $response->json();
    }
}
