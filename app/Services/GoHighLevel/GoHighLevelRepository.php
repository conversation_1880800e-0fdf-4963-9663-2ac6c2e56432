<?php

namespace App\Services\GoHighLevel;

use App\Models\GohighlevelLocation;
use App\Models\GoHighLevelToken;
use Exception;
use Generator;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

class GoHighLevelRepository
{
    private ?GoHighLevelToken $goHighLevelToken = null;

    public function __construct(
        private IGoHighLevelClient $client
    ) {
    }


    public function getGoHighLevelLocation(int $locationId): ?GohighlevelLocation
    {
        $gohighlevelLocation = GohighlevelLocation::find($locationId);
        if (!$gohighlevelLocation) {
            throw new Exception("Given gohighlevel_locations.id is not exists!");
        }
        return $gohighlevelLocation;
    }

    public function getAllLocations(?array $locationIds = []): ?Collection
    {
        $query = GohighlevelLocation::query();
        if ($locationIds) {
            $query->whereIn('id', $locationIds);
        }

        return $query->get();
    }

    public function getRedirectUri(int $locationId): string
    {
        return route('admin.gohighlevel.locations.code', ['location_id' => $locationId]);
    }

    public function getAuthorizationUrl(int $locationId): string
    {
        $gohighlevelLocation = $this->getGoHighLevelLocation($locationId);

        // If private_integration_key exists, return the client to the Private Integration Authorization URL
        if (!empty($gohighlevelLocation->private_integration_key)) {
            return $this->client->getPrivateIntegrationAuthorizationUrl(
                baseUrl: $gohighlevelLocation->base_url_api,
                privateIntegrationKey: $gohighlevelLocation->private_integration_key,
            );
        }

        $redirectUri = route('admin.gohighlevel.locations.code', ['location_id' => $locationId]);

        return $this->client->getAuthorizationUrl(
            baseUrlAuth: $gohighlevelLocation->base_url_authorization,
            clientId: $gohighlevelLocation->client_id ?? null,
            redirectUri: $redirectUri
        );
    }

    public function saveTokensByCode(int $locationId, string $code): ?GoHighLevelToken
    {
        $gohighlevelLocation = $this->getGoHighLevelLocation($locationId);

        // If private_integration_key exists, we don't need to save OAuth tokens
        if (!empty($gohighlevelLocation->private_integration_key)) {
            return null;
        }

        // If no private_integration_key, then client_id and client_secret are required
        if (empty($gohighlevelLocation->client_id) || empty($gohighlevelLocation->client_secret)) {
            throw new Exception("Client ID and Client Secret are required for token exchange when not using Private Integration Key");
        }

        $response = $this->client->getTokensByCode(
            baseUrl: $gohighlevelLocation->base_url_api,
            clientId: $gohighlevelLocation->client_id,
            clientSecret: $gohighlevelLocation->client_secret,
            code: $code
        );
        $gohighlevelTokens = new GoHighLevelToken();
        $gohighlevelTokens->gohighlevel_location_id = $gohighlevelLocation->id;
        $gohighlevelTokens->data = $response;
        $gohighlevelTokens->save();
        return $gohighlevelTokens;
    }

    public function getLastTokenRow(int $locationId): ?GoHighLevelToken
    {
        if (!$this->goHighLevelToken) {
            $this->goHighLevelToken = GoHighLevelToken::where([
                'gohighlevel_location_id' => $locationId
            ])->orderBy('created_at', 'desc')->first();
        }

        return $this->goHighLevelToken;
    }

    public function isTokenRowValid(GoHighLevelToken $tokenRow): bool
    {
        return strtotime($tokenRow->getCreatedAt()) + $tokenRow->getExpiresIn() > time();
    }

    public function refreshLocationToken(int $locationId): ?GoHighLevelToken
    {
        $location = GohighlevelLocation::find($locationId);

        // If private_integration_key exists, we don't need to refresh OAuth tokens
        if (!empty($location->private_integration_key)) {
            return null;
        }

        // If no private_integration_key, then client_id and client_secret are required
        if (empty($location->client_id) || empty($location->client_secret)) {
            throw new Exception("Client ID and Client Secret are required for token refresh when not using Private Integration Key");
        }

        $lastTokenRow = $this->getLastTokenRow($locationId);

        if (!$lastTokenRow) {
            throw new Exception("No token found to refresh for location ID: {$locationId}");
        }

        $refreshToken = $lastTokenRow['data']['refresh_token'];
        $response = $this->client->refreshToken(
            baseUrl: $location->base_url_api,
            clientId: $location->client_id,
            clientSecret: $location->client_secret,
            refreshToken: $refreshToken
        );
        $gohighlevelToken = new GoHighLevelToken();
        $gohighlevelToken->gohighlevel_location_id = $locationId;
        $gohighlevelToken->data = $response;
        $gohighlevelToken->save();

        return $gohighlevelToken;
    }

    public function refreshTokens(): Generator|GoHighLevelToken
    {
        try {
            $locations = $this->getAllLocations();

            foreach ($locations as $location) {
                /** @var GohighlevelLocation $location */
                $gohighlevelToken = $this->refreshLocationToken($location->id);

                yield $gohighlevelToken->toArray();
            }
            return $gohighlevelToken;
        } catch (Throwable $t) {
            throw $t;
        }
    }

    public function getPipelines(int $locationId): ?array
    {
        try {
            $location = $this->getGoHighLevelLocation($locationId);
            $token = $this->getLastTokenRow($locationId);

            return $this->client->getPipelines(
                baseUrl: $location->base_url_api,
                accessToken: $token->getAccessToken(),
                locationId: $token->getLocationId()
            );
        } catch (Throwable $t) {
            throw $t;
        }
    }

    public function getOpportunity(int $locationId, string $opportunityId): ?array
    {
        try {
            $location = $this->getGoHighLevelLocation($locationId);
            $token = $this->getLastTokenRow($locationId);

            return $this->client->getOpportunity(
                baseUrl: $location->base_url_api,
                accessToken: $token->getAccessToken(),
                opportunityId: $opportunityId
            );
        } catch (Throwable $t) {
            throw $t;
        }
    }

    public function getContact(int $locationId, string $contactId): ?array
    {
        try {
            $location = $this->getGoHighLevelLocation($locationId);
            $token = $this->getLastTokenRow($locationId);

            return $this->client->getContact(
                baseUrl: $location->base_url_api,
                accessToken: $token->getAccessToken(),
                contactId: $contactId
            );
        } catch (Throwable $t) {
            throw $t;
        }
    }

    public function getSubAccount(int $locationId): ?array
    {
        try {
            $location = $this->getGoHighLevelLocation($locationId);
            $token = $this->getLastTokenRow($locationId);

            return $this->client->getSubAccount(
                baseUrl: $location->base_url_api,
                accessToken: $token->getAccessToken(),
                locationId: $token->getLocationId()
            );
        } catch (Throwable $t) {
            throw $t;
        }
    }
}
