<?php

namespace App\Services\GoHighLevel;

use App\Services\FreshcarApi\IFreshcarApiRepository;
use Illuminate\Support\Facades\Log;

class GoHighLevelService
{
    public function __construct(
        private GoHighLevelRepository $goHighLevelRepository,
        private IFreshcarApiRepository $freshcarApiRepository
    ) {
    }

    /**
     * @deprecated This method is deprecated. Booking logic has been moved to OpportunityStatusUpdateHandler.
     */
    public function book(array $data): void
    {
        Log::channel('webhook')->warning('GoHighLevelService::book() is deprecated. Use OpportunityStatusUpdateHandler directly.', [
            'data_keys' => array_keys($data)
        ]);

        // Legacy method - kept for backward compatibility
        // The actual booking logic is now handled in OpportunityStatusUpdateHandler
    }
}
