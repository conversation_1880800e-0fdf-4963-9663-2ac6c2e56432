<?php

namespace App\Services\GoHighLevel;

interface IGoHighLevelClient
{
    public function getAuthorizationUrl(string $baseUrlAuth, string $clientId, string $redirectUri): string;
    public function getPrivateIntegrationAuthorizationUrl(string $baseUrl, string $privateIntegrationKey): string;
    public function getTokensByCode(string $baseUrl, string $clientId, string $clientSecret, string $code): ?array;
    public function refreshToken(string $baseUrl, string $clientId, string $clientSecret, string $refreshToken): ?array;

    public function getPipelines(string $baseUrl, string $accessToken, string $locationId): ?array;
    public function getOpportunity(string $baseUrl, string $accessToken, string $opportunityId): ?array;
    public function getContact(string $baseUrl, string $accessToken, string $contactId): ?array;
    public function getSubAccount(string $baseUrl, string $accessToken, string $locationId): ?array;

    public function getPIPipelines(string $baseUrl, string $privateIntegrationKey, string $locationId): ?array;
    public function getPIOpportunity(string $baseUrl, string $privateIntegrationKey, string $opportunityId): ?array;
    public function getPIContact(string $baseUrl, string $privateIntegrationKey, string $contactId): ?array;
    public function getPISubAccount(string $baseUrl, string $privateIntegrationKey, string $locationId): ?array;
}
