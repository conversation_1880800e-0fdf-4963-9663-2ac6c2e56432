<?php

namespace App\Services\GoHighLevel\WebhookHandlers;

use App\Services\WebhookActivityService;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\Jobs\ProcessWebhookJob;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class DefaultH<PERSON>ler extends ProcessWebhookJob
{
    public function handle()
    {
        $webhookActivityService = resolve(WebhookActivityService::class);
        $startTime = microtime(true);

        try {
            // Log webhook received
            $webhookActivityService->logWebhookReceived($this->webhookCall);

            // Log processing started
            $webhookActivityService->logProcessingStarted($this->webhookCall);

            // Log successful completion
            $processingTime = microtime(true) - $startTime;
            $webhookActivityService->logProcessingCompleted(
                $this->webhookCall,
                $processingTime,
                ['handler_type' => 'default']
            );

            return response()->json(['status' => 'success'], Response::HTTP_OK);
        } catch (Throwable $t) {
            Log::channel('webhook')->error('gohighlevel:default:error: ' . $t->getMessage());

            $webhookActivityService->logProcessingFailed(
                $this->webhookCall,
                $t->getMessage(),
                [
                    'handler_type' => 'default',
                    'exception_class' => get_class($t),
                    'file' => $t->getFile(),
                    'line' => $t->getLine(),
                ]
            );

            return response()->json(['status' => 'error'], Response::HTTP_OK);
        }
    }
}
