<?php

namespace App\Services\GoHighLevel\WebhookHandlers;

use App\Helpers\CryptoHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\SignatureValidator\SignatureValidator;
use <PERSON><PERSON>\WebhookClient\WebhookConfig;

class SignatureHandler implements SignatureValidator
{
    public function isValid(Request $request, WebhookConfig $config): bool
    {
        $signature = $request->header($config->signatureHeaderName);
        $payload = $request->getContent();

        // If no signature is provided, allow the webhook through
        if (empty($signature)) {
            Log::warning('webhook:gohighlevel:signature-validation:no-signature-provided', [
                'signature_header_name' => $config->signatureHeaderName,
                'message' => 'No signature header found - allowing webhook through',
                'payload_length' => strlen($payload),
                'request_headers' => array_keys($request->headers->all())
            ]);
            return true;
        }

        // If signature is provided, validate it
        $publicKey = file_get_contents(base_path() . '/' . $config->signingSecret, true);

        Log::info('webhook:gohighlevel:signature-validation:validating-signature', [
            'signature_header_name' => $config->signatureHeaderName,
            'signature_received' => $signature,
            'signature_length' => strlen($signature),
            'payload_length' => strlen($payload),
            'public_key_exists' => file_exists(base_path() . '/' . $config->signingSecret)
        ]);

        $verificationResult = CryptoHelper::verifySignature($payload, $signature, $publicKey);

        Log::info('webhook:gohighlevel:signature-validation:result', [
            'verification_result' => $verificationResult,
            'signature_provided' => true,
            'payload_hash' => hash('sha256', $payload)
        ]);

        return $verificationResult;
    }
}
