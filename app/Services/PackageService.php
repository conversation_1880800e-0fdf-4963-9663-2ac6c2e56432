<?php

namespace App\Services;

use App\Models\Package;
use App\Services\Contracts\PackageServiceInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Exception;

class PackageService implements PackageServiceInterface
{
    public function getAllPackages(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Package::query()->with('serviceGroup');

        $this->applyFilters($query, $filters);

        return $query->ordered()
            ->paginate($perPage)
            ->withQueryString();
    }

    public function getAllActivePackages(): Collection
    {
        return Package::active()
            ->with('serviceGroup')
            ->ordered()
            ->get();
    }

    public function getPackagesByServiceGroup(int $serviceGroupId, bool $activeOnly = true): Collection
    {
        $query = Package::byServiceGroup($serviceGroupId)->with('serviceGroup');

        if ($activeOnly) {
            $query->active();
        }

        return $query->ordered()->get();
    }

    public function findPackage(int $id): ?Package
    {
        return Package::with('serviceGroup')->find($id);
    }

    public function createPackage(array $data): Package
    {
        return Package::create([
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'duration_minutes' => $data['duration_minutes'] ?? null,
            'included_features' => $data['included_features'] ?? null,
            'feature_description' => $data['feature_description'] ?? null,
            'standard_price' => $data['standard_price'] ?? null,
            'higher_price' => $data['higher_price'] ?? null,
            'lower_price' => $data['lower_price'] ?? null,
            'currency' => $data['currency'] ?? 'GBP',
            'service_group_id' => $data['service_group_id'],
            'is_active' => $data['is_active'] ?? true,
            'sort_order' => $data['sort_order'] ?? 0,
        ]);
    }

    public function updatePackage(Package $package, array $data): Package
    {
        $package->update([
            'name' => $data['name'],
            'description' => $data['description'] ?? $package->description,
            'duration_minutes' => $data['duration_minutes'] ?? $package->duration_minutes,
            'included_features' => $data['included_features'] ?? $package->included_features,
            'feature_description' => $data['feature_description'] ?? $package->feature_description,
            'standard_price' => $data['standard_price'] ?? $package->standard_price,
            'higher_price' => $data['higher_price'] ?? $package->higher_price,
            'lower_price' => $data['lower_price'] ?? $package->lower_price,
            'currency' => $data['currency'] ?? $package->currency,
            'service_group_id' => $data['service_group_id'] ?? $package->service_group_id,
            'is_active' => $data['is_active'] ?? $package->is_active,
            'sort_order' => $data['sort_order'] ?? $package->sort_order,
        ]);

        return $package->fresh('serviceGroup');
    }

    public function deletePackage(Package $package): bool
    {
        // Check if package is being used in subscriptions
        if ($package->carClubSubscriptions()->count() > 0) {
            throw new Exception('Cannot delete package that is being used in active subscriptions.');
        }

        return $package->delete();
    }

    public function toggleActiveStatus(Package $package): Package
    {
        $package->update([
            'is_active' => !$package->is_active
        ]);

        return $package->fresh('serviceGroup');
    }

    public function duplicatePackage(Package $package, array $overrides = []): Package
    {
        $data = array_merge([
            'name' => $package->name . ' (Copy)',
            'description' => $package->description,
            'duration_minutes' => $package->duration_minutes,
            'included_features' => $package->included_features,
            'feature_description' => $package->feature_description,
            'standard_price' => $package->standard_price,
            'higher_price' => $package->higher_price,
            'lower_price' => $package->lower_price,
            'currency' => $package->currency,
            'service_group_id' => $package->service_group_id,
            'is_active' => false, // New packages start as inactive
            'sort_order' => $package->sort_order,
        ], $overrides);

        return $this->createPackage($data);
    }

    private function applyFilters(Builder $query, array $filters): void
    {
        // Use LIKE for SQLite, ILIKE for PostgreSQL
        $operator = config('database.default') === 'sqlite' ? 'LIKE' : 'ILIKE';

        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search, $operator) {
                $q->where('name', $operator, "%{$search}%")
                  ->orWhere('description', $operator, "%{$search}%")
                  ->orWhere('feature_description', $operator, "%{$search}%");
            });
        }

        if (!empty($filters['service_group_id'])) {
            $query->where('service_group_id', $filters['service_group_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (!empty($filters['price_min'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('standard_price', '>=', $filters['price_min'])
                  ->orWhere('higher_price', '>=', $filters['price_min'])
                  ->orWhere('lower_price', '>=', $filters['price_min']);
            });
        }

        if (!empty($filters['price_max'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('standard_price', '<=', $filters['price_max'])
                  ->orWhere('higher_price', '<=', $filters['price_max'])
                  ->orWhere('lower_price', '<=', $filters['price_max']);
            });
        }
    }
}
