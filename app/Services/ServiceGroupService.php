<?php

namespace App\Services;

use App\Models\ServiceGroup;
use App\Services\Contracts\ServiceGroupServiceInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Exception;

class ServiceGroupService implements ServiceGroupServiceInterface
{
    public function getAllServiceGroups(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = ServiceGroup::query()->with('packages');

        $this->applyFilters($query, $filters);

        return $query->ordered()
            ->paginate($perPage)
            ->withQueryString();
    }

    public function getAllActiveServiceGroups(): Collection
    {
        return ServiceGroup::active()
            ->ordered()
            ->get();
    }

    public function findServiceGroup(int $id): ?ServiceGroup
    {
        return ServiceGroup::with('packages')->find($id);
    }

    public function createServiceGroup(array $data): ServiceGroup
    {
        return ServiceGroup::create([
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'is_active' => $data['is_active'] ?? true,
            'sort_order' => $data['sort_order'] ?? 0,
        ]);
    }

    public function updateServiceGroup(ServiceGroup $serviceGroup, array $data): ServiceGroup
    {
        $serviceGroup->update([
            'name' => $data['name'],
            'description' => $data['description'] ?? $serviceGroup->description,
            'is_active' => $data['is_active'] ?? $serviceGroup->is_active,
            'sort_order' => $data['sort_order'] ?? $serviceGroup->sort_order,
        ]);

        return $serviceGroup->fresh();
    }

    public function deleteServiceGroup(ServiceGroup $serviceGroup): bool
    {
        // Check if service group has packages
        if ($serviceGroup->packages()->count() > 0) {
            throw new Exception('Cannot delete service group that has packages. Please delete or reassign packages first.');
        }

        return $serviceGroup->delete();
    }

    public function toggleActiveStatus(ServiceGroup $serviceGroup): ServiceGroup
    {
        $serviceGroup->update([
            'is_active' => !$serviceGroup->is_active
        ]);

        return $serviceGroup->fresh();
    }

    private function applyFilters(Builder $query, array $filters): void
    {
        // Use LIKE for SQLite, ILIKE for PostgreSQL
        $operator = config('database.default') === 'sqlite' ? 'LIKE' : 'ILIKE';

        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search, $operator) {
                $q->where('name', $operator, "%{$search}%")
                  ->orWhere('description', $operator, "%{$search}%");
            });
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }
    }
}
