<?php

namespace App\Services;

use App\Models\GohighlevelLocation;
use App\Traits\ActivityLog;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\Models\WebhookCall;

class WebhookActivityService
{
    use ActivityLog;

    public const SIGNATURE_SUCCESS = 'success';
    public const SIGNATURE_FAILURE = 'failure';
    public const SIGNATURE_NA = 'n/a';

    public const LOG_WEBHOOK_RECEIVED = 'webhook.received';
    public const LOG_WEBHOOK_SIGNATURE_VALIDATED = 'webhook.signature.validated';
    public const LOG_WEBHOOK_PROCESSING_STARTED = 'webhook.processing.started';
    public const LOG_WEBHOOK_EMAIL_CHECK = 'webhook.email.check';
    public const LOG_WEBHOOK_BOOKING_CREATED = 'webhook.booking.created';
    public const LOG_WEBHOOK_PROCESSING_COMPLETED = 'webhook.processing.completed';
    public const LOG_WEBHOOK_PROCESSING_FAILED = 'webhook.processing.failed';

    public function log(string $msg, array $context, string $channel = 'webhook'): void
    {
        $this->activity($msg, null, null, $context);
        Log::channel($channel)->info($msg, $context);
    }

    public function logWebhookReceived(WebhookCall $webhookCall, array $additionalData = []): void
    {
        // Handle payload size calculation safely
        $payloadSize = 0;
        if (is_string($webhookCall->payload)) {
            $payloadSize = strlen($webhookCall->payload);
        } elseif (is_array($webhookCall->payload)) {
            $payloadSize = strlen(json_encode($webhookCall->payload));
        }

        // Extract location and webhook information from payload
        $locationInfo = $this->extractLocationInfo($webhookCall);

        $properties = array_merge([
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'payload_size' => $payloadSize,
            'headers' => $webhookCall->headers,
            'url' => $webhookCall->url,
        ], $locationInfo, $additionalData);

        $this->activity(
            self::LOG_WEBHOOK_RECEIVED,
            null,
            $webhookCall,
            $properties
        );

        Log::channel('webhook')->info('webhook-activity:received', $properties);
    }

    public function logSignatureValidation(
        WebhookCall $webhookCall,
        string $validationResult,
        array $additionalData = []
    ): void {
        $locationInfo = $this->extractLocationInfo($webhookCall);

        $properties = array_merge([
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'signature_validation' => $validationResult,
            'validation_timestamp' => now()->toISOString(),
        ], $locationInfo, $additionalData);

        $this->activity(
            self::LOG_WEBHOOK_SIGNATURE_VALIDATED,
            null,
            $webhookCall,
            $properties
        );

        Log::channel('webhook')->info('webhook-activity:signature-validated', $properties);
    }

    public function logProcessingStarted(WebhookCall $webhookCall, array $additionalData = []): void
    {
        $locationInfo = $this->extractLocationInfo($webhookCall);

        $properties = array_merge([
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'processing_started_at' => now()->toISOString(),
        ], $locationInfo, $additionalData);

        $this->activity(
            self::LOG_WEBHOOK_PROCESSING_STARTED,
            null,
            $webhookCall,
            $properties
        );

        Log::channel('webhook')->info('webhook-activity:processing-started', $properties);
    }

    public function logEmailCheck(
        WebhookCall $webhookCall,
        string $email,
        bool $isEmailAvailable,
        array $additionalData = []
    ): void {
        $locationInfo = $this->extractLocationInfo($webhookCall);

        $properties = array_merge([
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'customer_email' => $email,
            'is_new_customer' => $isEmailAvailable,
            'customer_type' => $isEmailAvailable ? 'new' : 'existing',
        ], $locationInfo, $additionalData);

        $this->activity(
            self::LOG_WEBHOOK_EMAIL_CHECK,
            null,
            $webhookCall,
            $properties
        );

        Log::channel('webhook')->info('webhook-activity:email-check', $properties);
    }

    public function logBookingCreated(
        WebhookCall $webhookCall,
        string $email,
        string $bookingType,
        $result,
        array $additionalData = []
    ): void {
        $locationInfo = $this->extractLocationInfo($webhookCall);

        $properties = array_merge([
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'customer_email' => $email,
            'booking_type' => $bookingType,
            'booking_result' => $result,
        ], $locationInfo, $additionalData);

        $this->activity(
            self::LOG_WEBHOOK_BOOKING_CREATED,
            null,
            $webhookCall,
            $properties
        );

        Log::channel('webhook')->info('webhook-activity:booking-created', $properties);
    }

    public function logProcessingCompleted(
        WebhookCall $webhookCall,
        float $processingTimeSeconds,
        array $additionalData = []
    ): void {
        $locationInfo = $this->extractLocationInfo($webhookCall);

        $properties = array_merge([
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'processing_completed_at' => now()->toISOString(),
            'processing_time_seconds' => $processingTimeSeconds,
            'status' => 'completed',
        ], $locationInfo, $additionalData);

        $this->activity(
            self::LOG_WEBHOOK_PROCESSING_COMPLETED,
            null,
            $webhookCall,
            $properties
        );

        Log::channel('webhook')->info('webhook-activity:processing-completed', $properties);
    }

    public function logProcessingFailed(
        WebhookCall $webhookCall,
        string $errorMessage,
        array $additionalData = []
    ): void {
        $locationInfo = $this->extractLocationInfo($webhookCall);

        $properties = array_merge([
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'processing_failed_at' => now()->toISOString(),
            'error_message' => $errorMessage,
            'status' => 'failed',
        ], $locationInfo, $additionalData);

        $this->activity(
            self::LOG_WEBHOOK_PROCESSING_FAILED,
            null,
            $webhookCall,
            $properties
        );

        Log::channel('webhook')->error('webhook-activity:processing-failed', $properties);
    }

    public function getSignatureValidationStates(): array
    {
        return [
            self::SIGNATURE_SUCCESS => 'Success',
            self::SIGNATURE_FAILURE => 'Failure',
            self::SIGNATURE_NA => 'N/A',
        ];
    }

    /**
     * Extract location and webhook information from the webhook payload
     */
    private function extractLocationInfo(WebhookCall $webhookCall): array
    {
        $payload = $webhookCall->payload;

        // Handle both string and array payloads
        if (is_string($payload)) {
            $payload = json_decode($payload, true) ?? [];
        } elseif (!is_array($payload)) {
            $payload = [];
        }

        $locationInfo = [
            'ghl_location_id' => null,
            'ghl_location_name' => null,
            'ghl_webhook_id' => null,
        ];

        // Extract GoHighLevel location ID
        if (isset($payload['locationId'])) {
            $locationInfo['ghl_location_id'] = $payload['locationId'];

            // Look up location name from database
            $location = GohighlevelLocation::where('location_id', $payload['locationId'])->first();
            if ($location) {
                $locationInfo['ghl_location_name'] = $location->name;
            }
        }

        // Extract GoHighLevel webhook ID (different from our internal webhook_id)
        if (isset($payload['webhookId'])) {
            $locationInfo['ghl_webhook_id'] = $payload['webhookId'];
        }

        return $locationInfo;
    }
}
