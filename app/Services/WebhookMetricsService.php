<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class WebhookMetricsService
{
    public function getTodayMetrics(): array
    {
        $today = Carbon::today();

        return [
            'total_webhooks' => $this->getWebhookCount($today),
            'successful_webhooks' => $this->getSuccessfulWebhookCount($today),
            'failed_webhooks' => $this->getFailedWebhookCount($today),
            'success_rate' => $this->getSuccessRate($today),
            'average_processing_time' => $this->getAverageProcessingTime($today),
            'new_customers' => $this->getNewCustomerCount($today),
            'existing_customers' => $this->getExistingCustomerCount($today),
        ];
    }

    public function getWeeklyMetrics(): array
    {
        $weekStart = Carbon::now()->startOfWeek();

        return [
            'total_webhooks' => $this->getWebhookCount($weekStart),
            'successful_webhooks' => $this->getSuccessfulWebhookCount($weekStart),
            'failed_webhooks' => $this->getFailedWebhookCount($weekStart),
            'success_rate' => $this->getSuccessRate($weekStart),
            'average_processing_time' => $this->getAverageProcessingTime($weekStart),
            'new_customers' => $this->getNewCustomerCount($weekStart),
            'existing_customers' => $this->getExistingCustomerCount($weekStart),
        ];
    }

    public function getMonthlyMetrics(): array
    {
        $monthStart = Carbon::now()->startOfMonth();

        return [
            'total_webhooks' => $this->getWebhookCount($monthStart),
            'successful_webhooks' => $this->getSuccessfulWebhookCount($monthStart),
            'failed_webhooks' => $this->getFailedWebhookCount($monthStart),
            'success_rate' => $this->getSuccessRate($monthStart),
            'average_processing_time' => $this->getAverageProcessingTime($monthStart),
            'new_customers' => $this->getNewCustomerCount($monthStart),
            'existing_customers' => $this->getExistingCustomerCount($monthStart),
        ];
    }

    public function getSignatureValidationMetrics(Carbon $since = null): array
    {
        $since = $since ?? Carbon::today();

        $signatureStats = Activity::where('description', WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED)
            ->where('created_at', '>=', $since)
            ->get()
            ->groupBy(function ($activity) {
                $properties = [];
                if ($activity->properties instanceof \Illuminate\Support\Collection) {
                    $properties = $activity->properties->toArray();
                } elseif (is_array($activity->properties)) {
                    $properties = $activity->properties;
                }
                return $properties['signature_validation'] ?? 'unknown';
            })
            ->map(function ($group) {
                return $group->count();
            });

        $success = $signatureStats->get(WebhookActivityService::SIGNATURE_SUCCESS, 0);
        $failure = $signatureStats->get(WebhookActivityService::SIGNATURE_FAILURE, 0);
        $na = $signatureStats->get(WebhookActivityService::SIGNATURE_NA, 0);
        $total = $signatureStats->sum();

        return [
            'success' => $success,
            'failure' => $failure,
            'na' => $na,
            'total' => $total,
            'success_percentage' => $total > 0 ? round(($success / $total) * 100, 1) : 0,
            'failure_percentage' => $total > 0 ? round(($failure / $total) * 100, 1) : 0,
            'na_percentage' => $total > 0 ? round(($na / $total) * 100, 1) : 0,
        ];
    }

    public function getHourlyVolumeData(Carbon $since = null): Collection
    {
        $since = $since ?? Carbon::today();

        // Use database-agnostic approach for extracting hour
        $hourExpression = match (config('database.default')) {
            'mysql', 'mariadb' => 'HOUR(created_at)',
            'pgsql' => 'EXTRACT(HOUR FROM created_at)',
            'sqlite' => 'strftime("%H", created_at)',
            default => 'strftime("%H", created_at)',
        };

        return Activity::where('description', WebhookActivityService::LOG_WEBHOOK_RECEIVED)
            ->where('created_at', '>=', $since)
            ->select(
                DB::raw("{$hourExpression} as hour"),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();
    }

    public function getRecentWebhookActivity(int $limit = 50): Collection
    {
        return Activity::whereIn('description', [
            WebhookActivityService::LOG_WEBHOOK_RECEIVED,
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED,
            WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED,
        ])
            ->with('subject')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    private function getWebhookCount(Carbon $since): int
    {
        return Activity::where('description', WebhookActivityService::LOG_WEBHOOK_RECEIVED)
            ->where('created_at', '>=', $since)
            ->count();
    }

    private function getSuccessfulWebhookCount(Carbon $since): int
    {
        return Activity::where('description', WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED)
            ->where('created_at', '>=', $since)
            ->count();
    }

    private function getFailedWebhookCount(Carbon $since): int
    {
        return Activity::where('description', WebhookActivityService::LOG_WEBHOOK_PROCESSING_FAILED)
            ->where('created_at', '>=', $since)
            ->count();
    }

    private function getSuccessRate(Carbon $since): float
    {
        $total = $this->getWebhookCount($since);
        if ($total === 0) {
            return 0.0;
        }

        $successful = $this->getSuccessfulWebhookCount($since);
        return round(($successful / $total) * 100, 2);
    }

    private function getAverageProcessingTime(Carbon $since): float
    {
        $activities = Activity::where('description', WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED)
            ->where('created_at', '>=', $since)
            ->get();

        $processingTimes = $activities
            ->map(function ($activity) {
                $properties = is_array($activity->properties) ? $activity->properties : [];
                return $properties['processing_time_seconds'] ?? null;
            })
            ->filter(function ($time) {
                return $time !== null && is_numeric($time);
            })
            ->map(function ($time) {
                return (float) $time;
            });

        return $processingTimes->isEmpty() ? 0.0 : round($processingTimes->average(), 2);
    }

    private function getNewCustomerCount(Carbon $since): int
    {
        return Activity::where('description', WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK)
            ->where('created_at', '>=', $since)
            ->get()
            ->filter(function ($activity) {
                $properties = is_array($activity->properties) ? $activity->properties : [];
                return ($properties['is_new_customer'] ?? false) === true;
            })
            ->count();
    }

    private function getExistingCustomerCount(Carbon $since): int
    {
        return Activity::where('description', WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK)
            ->where('created_at', '>=', $since)
            ->get()
            ->filter(function ($activity) {
                $properties = is_array($activity->properties) ? $activity->properties : [];
                return ($properties['is_new_customer'] ?? true) === false;
            })
            ->count();
    }
}
