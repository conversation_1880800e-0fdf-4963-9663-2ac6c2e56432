apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: laravel-artisan-command-job
  labels:
    app: freshcar-core
    component: artisan-command
spec:
  template:
    spec:
      template:
        spec:
          taskCount: 1
          parallelism: 1
          taskTimeoutSeconds: 1800 # 30 minutes
          restartPolicy: Never
          containers:
            - name: laravel-artisan-command
              image: gcr.io/PROJECT_ID/api:latest
              command:
                [
                  "/var/www/html/cloud-run-jobs/entrypoints/artisan-command-entrypoint",
                ]
              resources:
                limits:
                  cpu: 1000m
                  memory: 512Mi
              env:
                - name: APP_ENV
                  value: "production"
                - name: APP_DEBUG
                  value: "false"
                - name: LOG_CHANNEL
                  value: "stderr"
                - name: DB_CONNECTION
                  value: "pgsql"
                - name: DB_HOST
                  value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
                - name: DB_PORT
                  value: "5432"
                - name: DB_DATABASE
                  value: "DB_DATABASE"
                - name: DB_USERNAME
                  value: "DB_USERNAME"
                - name: CACHE_DRIVER
                  value: "database"
                - name: ARTISAN_COMMAND
                  value: "inspire"
                - name: ARTISAN_ARGS
                  value: ""
                - name: APP_KEY
                  valueFrom:
                    secretKeyRef:
                      name: app-key
                      key: latest
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: db-password
                      key: latest
                - name: STRIPE_SECRET
                  valueFrom:
                    secretKeyRef:
                      name: stripe-secret
                      key: latest
          volumes:
            - name: cloudsql
              csi:
                driver: gcp-compute-persistent-disk-csi-driver
