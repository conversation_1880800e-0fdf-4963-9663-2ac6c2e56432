apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: laravel-cache-clear-job
  labels:
    app: freshcar-core
    component: cache-clear
spec:
  template:
    spec:
      template:
        spec:
          taskCount: 1
          parallelism: 1
          taskTimeoutSeconds: 600 # 10 minutes
          restartPolicy: Never
          containers:
            - name: laravel-cache-clear
              image: gcr.io/PROJECT_ID/api:latest
              command:
                [
                  "/var/www/html/cloud-run-jobs/entrypoints/cache-clear-entrypoint",
                ]
              resources:
                limits:
                  cpu: 500m
                  memory: 256Mi
              env:
                - name: APP_ENV
                  value: "production"
                - name: APP_DEBUG
                  value: "false"
                - name: LOG_CHANNEL
                  value: "stderr"
                - name: DB_CONNECTION
                  value: "pgsql"
                - name: DB_HOST
                  value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
                - name: DB_PORT
                  value: "5432"
                - name: DB_DATABASE
                  value: "DB_DATABASE"
                - name: DB_USERNAME
                  value: "DB_USERNAME"
                - name: CACHE_DRIVER
                  value: "database"
                - name: APP_KEY
                  valueFrom:
                    secretKeyRef:
                      name: app-key
                      key: latest
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: db-password
                      key: latest
          volumes:
            - name: cloudsql
              csi:
                driver: gcp-compute-persistent-disk-csi-driver
