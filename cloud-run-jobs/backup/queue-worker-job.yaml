apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: laravel-queue-worker-job
  labels:
    app: freshcar-core
    component: queue-worker
spec:
  template:
    spec:
      template:
        spec:
          taskCount: 1
          parallelism: 1
          taskTimeoutSeconds: 3600 # 1 hour
          restartPolicy: Never
          containers:
            - name: laravel-queue-worker
              image: gcr.io/PROJECT_ID/api:latest
              command:
                [
                  "/var/www/html/cloud-run-jobs/entrypoints/queue-worker-entrypoint",
                ]
              resources:
                limits:
                  cpu: 1000m
                  memory: 1Gi
              env:
                - name: APP_ENV
                  value: "production"
                - name: APP_DEBUG
                  value: "false"
                - name: LOG_CHANNEL
                  value: "stderr"
                - name: DB_CONNECTION
                  value: "pgsql"
                - name: DB_HOST
                  value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
                - name: DB_PORT
                  value: "5432"
                - name: DB_DATABASE
                  value: "DB_DATABASE"
                - name: DB_USERNAME
                  value: "DB_USERNAME"
                - name: CACHE_DRIVER
                  value: "database"
                - name: QUEUE_CONNECTION
                  value: "database"
                - name: QUEUE_NAME
                  value: "default"
                - name: WORKER_TIMEOUT
                  value: "300"
                - name: WORKER_TRIES
                  value: "3"
                - name: WORKER_MAX_JOBS
                  value: "100"
                - name: APP_KEY
                  valueFrom:
                    secretKeyRef:
                      name: app-key
                      key: latest
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: db-password
                      key: latest
                - name: STRIPE_SECRET
                  valueFrom:
                    secretKeyRef:
                      name: stripe-secret
                      key: latest
          volumes:
            - name: cloudsql
              csi:
                driver: gcp-compute-persistent-disk-csi-driver
