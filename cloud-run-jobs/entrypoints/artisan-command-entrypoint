#!/bin/bash

# Laravel Artisan Command Job Entrypoint
# This script runs custom Artisan commands in Cloud Run Jobs

set -e

echo "Starting Laravel Artisan Command Job..."
echo "Timestamp: $(date)"
echo "Environment: $APP_ENV"
echo "Command: ${ARTISAN_COMMAND:-inspire}"
echo "Arguments: ${ARTISAN_ARGS:-}"

# Set proper permissions
chmod -R 755 /var/www/html/storage /var/www/html/bootstrap/cache

# Clear any cached configuration to ensure fresh environment
echo "Clearing Laravel caches..."
php artisan config:clear

# Cache configuration for better performance
echo "Caching configuration..."
php artisan config:cache

# Check database connection if command requires it
if [ "$SKIP_DB_CHECK" != "true" ]; then
    echo "Testing database connection..."
    php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connection successful';" || {
        echo "Database connection failed. Set SKIP_DB_CHECK=true if this command doesn't need database access."
        exit 1
    }
fi

# Build the full artisan command
FULL_COMMAND="php artisan ${ARTISAN_COMMAND:-inspire}"

if [ -n "$ARTISAN_ARGS" ]; then
    FULL_COMMAND="$FULL_COMMAND $ARTISAN_ARGS"
fi

echo "Executing command: $FULL_COMMAND"

# Execute the artisan command
eval $FULL_COMMAND

echo "Artisan command job completed successfully!"
echo "Timestamp: $(date)"
