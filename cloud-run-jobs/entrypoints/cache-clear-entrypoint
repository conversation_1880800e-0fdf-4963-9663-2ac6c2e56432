#!/bin/bash

# Laravel Cache Clear Job Entrypoint
# This script clears various Laravel caches in Cloud Run Jobs

set -e

echo "Starting Laravel Cache Clear Job..."
echo "Timestamp: $(date)"
echo "Environment: $APP_ENV"

# Set proper permissions
chmod -R 755 /var/www/html/storage /var/www/html/bootstrap/cache

# Clear all caches
echo "Clearing application cache..."
php artisan cache:clear

echo "Clearing configuration cache..."
php artisan config:clear

echo "Clearing route cache..."
php artisan route:clear

echo "Clearing view cache..."
php artisan view:clear

echo "Clearing compiled services..."
php artisan clear-compiled

# Clear specific cache types if requested
if [ "$CLEAR_OPCACHE" = "true" ]; then
    echo "Clearing OPcache..."
    php artisan opcache:clear 2>/dev/null || echo "OPcache clear not available"
fi

if [ "$CLEAR_QUEUE_CACHE" = "true" ]; then
    echo "Clearing failed queue jobs..."
    php artisan queue:clear 2>/dev/null || echo "Queue clear not available"
fi

# Rebuild caches if requested
if [ "$REBUILD_CACHE" = "true" ]; then
    echo "Rebuilding configuration cache..."
    php artisan config:cache
    
    echo "Rebuilding route cache..."
    php artisan route:cache
    
    echo "Rebuilding view cache..."
    php artisan view:cache
fi

echo "Cache clear job completed successfully!"
echo "Timestamp: $(date)"
