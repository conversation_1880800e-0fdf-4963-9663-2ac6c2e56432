#!/bin/bash

# Laravel Migration Job Entrypoint
# This script runs database migrations in Cloud Run Jobs

set -e

echo "Starting Laravel Migration Job..."
echo "Timestamp: $(date)"
echo "Environment: $APP_ENV"

# Set proper permissions
chmod -R 755 /var/www/html/storage /var/www/html/bootstrap/cache

# Clear any cached configuration to ensure fresh environment
echo "Clearing Laravel caches..."
php artisan config:clear

# Use file cache driver for cache:clear to avoid database connection
php artisan cache:clear
php artisan route:clear
php artisan view:clear


# Run migrations
echo "Running database migrations..."
if [ "$MIGRATION_SEED" = "true" ]; then
    echo "Running migrations with seeders..."
    php artisan migrate --seed --force
else
    echo "Running migrations only..."
    php artisan migrate --force
fi

# Check migration status
echo "Migration status:"
php artisan migrate:status

echo "Migration job completed successfully!"
echo "Timestamp: $(date)"
