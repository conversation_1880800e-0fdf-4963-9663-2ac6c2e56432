#!/bin/bash

# Laravel Queue Worker Job Entrypoint
# This script runs queue workers in Cloud Run Jobs

set -e

echo "Starting Laravel Queue Worker Job..."
echo "Timestamp: $(date)"
echo "Environment: $APP_ENV"
echo "Queue: ${QUEUE_NAME:-default}"
echo "Timeout: ${WORKER_TIMEOUT:-300}"
echo "Max Jobs: ${WORKER_MAX_JOBS:-100}"
echo "Tries: ${WORKER_TRIES:-3}"

# Set proper permissions
chmod -R 755 /var/www/html/storage /var/www/html/bootstrap/cache

# Clear any cached configuration to ensure fresh environment
echo "Clearing Laravel caches..."
php artisan config:clear

# Cache configuration for better performance
echo "Caching configuration..."
php artisan config:cache

# Check database connection
echo "Testing database connection..."
php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connection successful';"

# Check queue connection
echo "Testing queue connection..."
php artisan queue:monitor

# Function to handle graceful shutdown
cleanup() {
    echo "Received shutdown signal, stopping queue worker gracefully..."
    kill -TERM $WORKER_PID 2>/dev/null || true
    wait $WORKER_PID 2>/dev/null || true
    echo "Queue worker stopped."
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Start queue worker
echo "Starting queue worker..."
php artisan queue:work \
    --queue="${QUEUE_NAME:-default}" \
    --timeout="${WORKER_TIMEOUT:-300}" \
    --tries="${WORKER_TRIES:-3}" \
    --max-jobs="${WORKER_MAX_JOBS:-100}" \
    --memory=512 \
    --verbose &

WORKER_PID=$!

# Wait for the worker process
wait $WORKER_PID

echo "Queue worker job completed!"
echo "Timestamp: $(date)"
