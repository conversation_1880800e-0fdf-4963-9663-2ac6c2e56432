apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: laravel-search-reindex-job
  labels:
    app: freshcar-core
    component: search-indexing
spec:
  template:
    spec:
      template:
        spec:
          taskCount: 1
          parallelism: 1
          taskTimeoutSeconds: 3600 # 1 hour
          restartPolicy: Never
          containers:
            - name: laravel-search-reindex
              image: gcr.io/PROJECT_ID/api:latest
              command:
                [
                  "/var/www/html/cloud-run-jobs/entrypoints/artisan-command-entrypoint",
                ]
              resources:
                limits:
                  cpu: 2000m
                  memory: 1Gi
              env:
                - name: APP_ENV
                  value: "production"
                - name: APP_DEBUG
                  value: "false"
                - name: LOG_CHANNEL
                  value: "stderr"
                - name: DB_CONNECTION
                  value: "pgsql"
                - name: DB_HOST
                  value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
                - name: DB_PORT
                  value: "5432"
                - name: DB_DATABASE
                  value: "DB_DATABASE"
                - name: DB_USERNAME
                  value: "DB_USERNAME"
                - name: CACHE_DRIVER
                  value: "database"
                - name: ARTISAN_COMMAND
                  value: "search:reindex"
                - name: ARTISAN_ARGS
                  value: "User"
                - name: APP_KEY
                  valueFrom:
                    secretKeyRef:
                      name: app-key
                      key: latest
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: db-password
                      key: latest
          volumes:
            - name: cloudsql
              csi:
                driver: gcp-compute-persistent-disk-csi-driver
