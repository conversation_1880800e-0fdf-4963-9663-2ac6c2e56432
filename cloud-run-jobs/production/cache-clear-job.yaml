apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: laravel-cache-clear-job-production
  labels:
    app: freshcar-core
    component: cache-clear
    environment: production
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cloudsql-instances: CLOUD_SQL_CONNECTION_NAME
    spec:
      parallelism: 1
      taskCount: 1
      template:
        spec:
          timeoutSeconds: 600 # 10 minutes
          serviceAccountName: tlg-devops@PROJECT_ID.iam.gserviceaccount.com
          containers:
            - name: laravel-cache-clear
              image: gcr.io/PROJECT_ID/api:production-latest
              command:
                [
                  "/var/www/html/cloud-run-jobs/entrypoints/cache-clear-entrypoint",
                ]
              resources:
                limits:
                  cpu: 1000m
                  memory: 512Mi
              env:
                - name: APP_ENV
                  value: "production"
                - name: APP_DEBUG
                  value: "false"
                - name: LOG_CHANNEL
                  value: "stderr"
                - name: DB_CONNECTION
                  value: "pgsql"
                - name: DB_PORT
                  value: "5432"
                - name: DB_SOCKET
                  value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
                - name: DB_DATABASE
                  value: "postgres"
                - name: DB_USERNAME
                  value: "postgres"
                - name: DATABASE_URL
                  value: ""
                - name: CACHE_DRIVER
                  value: "database"
                - name: APP_KEY
                  valueFrom:
                    secretKeyRef:
                      name: production-app-key
                      key: latest
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: production-db-password
                      key: latest
