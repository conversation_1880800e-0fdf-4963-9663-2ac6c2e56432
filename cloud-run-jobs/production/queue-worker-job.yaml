apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: laravel-queue-worker-job-production
  labels:
    app: freshcar-core
    component: queue-worker
    environment: production
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cloudsql-instances: CLOUD_SQL_CONNECTION_NAME
    spec:
      parallelism: 1
      taskCount: 1
      template:
        spec:
          timeoutSeconds: 3600 # 1 hour
          serviceAccountName: tlg-devops@PROJECT_ID.iam.gserviceaccount.com
          containers:
            - name: laravel-queue-worker
              image: gcr.io/PROJECT_ID/api:production-latest
              command:
                [
                  "/var/www/html/cloud-run-jobs/entrypoints/queue-worker-entrypoint",
                ]
              resources:
                limits:
                  cpu: 2000m
                  memory: 2Gi
              env:
                - name: APP_ENV
                  value: "production"
                - name: APP_DEBUG
                  value: "false"
                - name: LOG_CHANNEL
                  value: "stderr"
                - name: DB_CONNECTION
                  value: "pgsql"
                - name: DB_PORT
                  value: "5432"
                - name: DB_SOCKET
                  value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
                - name: DB_DATABASE
                  value: "postgres"
                - name: DB_USERNAME
                  value: "postgres"
                - name: DATABASE_URL
                  value: ""
                - name: CACHE_DRIVER
                  value: "database"
                - name: QUEUE_CONNECTION
                  value: "database"
                - name: QUEUE_NAME
                  value: "default"
                - name: WORKER_TIMEOUT
                  value: "300"
                - name: WORKER_TRIES
                  value: "3"
                - name: WORKER_MAX_JOBS
                  value: "100"
                - name: APP_KEY
                  valueFrom:
                    secretKeyRef:
                      name: production-app-key
                      key: latest
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: production-db-password
                      key: latest
                - name: STRIPE_SECRET
                  valueFrom:
                    secretKeyRef:
                      name: production-stripe-secret
                      key: latest
