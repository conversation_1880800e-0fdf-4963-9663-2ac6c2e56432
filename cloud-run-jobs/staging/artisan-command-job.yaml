apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: laravel-artisan-command-job-staging
  labels:
    app: freshcar-core
    component: artisan-command
    environment: staging
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cloudsql-instances: CLOUD_SQL_CONNECTION_NAME
    spec:
      parallelism: 1
      taskCount: 1
      template:
        spec:
          timeoutSeconds: 1800 # 30 minutes
          serviceAccountName: tlg-devops@PROJECT_ID.iam.gserviceaccount.com
          containers:
            - name: laravel-artisan-command
              image: gcr.io/PROJECT_ID/api:staging-latest
              command:
                [
                  "/var/www/html/cloud-run-jobs/entrypoints/artisan-command-entrypoint",
                ]
              resources:
                limits:
                  cpu: 1000m
                  memory: 512Mi
              env:
                - name: APP_ENV
                  value: "staging"
                - name: APP_DEBUG
                  value: "true"
                - name: LOG_CHANNEL
                  value: "stderr"
                - name: DB_CONNECTION
                  value: "pgsql"
                - name: DB_PORT
                  value: "5432"
                - name: DB_SOCKET
                  value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
                - name: DB_DATABASE
                  value: "postgres"
                - name: DB_USERNAME
                  value: "postgres"
                - name: DATABASE_URL
                  value: ""
                - name: CACHE_DRIVER
                  value: "database"
                - name: ARTISAN_COMMAND
                  value: "inspire"
                - name: ARTISAN_ARGS
                  value: ""
                - name: APP_KEY
                  valueFrom:
                    secretKeyRef:
                      name: staging-app-key
                      key: latest
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: staging-db-password
                      key: latest
                - name: STRIPE_SECRET
                  valueFrom:
                    secretKeyRef:
                      name: staging-stripe-secret
                      key: latest
