#!/bin/bash

# Laravel Queue Worker Service Entrypoint
# This script runs queue workers in Cloud Run Service (long-running)
# It also starts a simple HTTP server for Cloud Run health checks

set -e

echo "Starting Laravel Queue Worker Service..."
echo "Timestamp: $(date)"
echo "Environment: $APP_ENV"
echo "Queue: ${QUEUE_NAME:-default}"
echo "Port: ${PORT:-8080}"
echo "Working directory: $(pwd)"
echo "PHP version: $(php --version | head -1)"

# Set proper permissions
chmod -R 755 /var/www/html/storage /var/www/html/bootstrap/cache

# Clear any cached configuration to ensure fresh environment
echo "Clearing Laravel caches..."
php artisan config:clear

# Cache configuration for better performance
echo "Caching configuration..."
php artisan config:cache

# Check database connection
echo "Testing database connection..."
php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connection successful';"

# Create a simple health check endpoint using PHP built-in server
create_health_server() {
    local port=${PORT:-8080}

    # Create a simple health check script
    cat > /tmp/health.php << 'EOF'
<?php
header('Content-Type: application/json');
echo json_encode([
    'status' => 'healthy',
    'service' => 'queue-worker',
    'timestamp' => date('c'),
    'port' => $_SERVER['SERVER_PORT'] ?? 'unknown'
]);
EOF

    # Start PHP built-in server in background
    echo "Starting health check server on 0.0.0.0:$port"
    php -S 0.0.0.0:$port -t /tmp /tmp/health.php &
    HEALTH_SERVER_PID=$!
    echo "Health check server started with PID: $HEALTH_SERVER_PID"
}

# Function to handle graceful shutdown
cleanup() {
    echo "Received shutdown signal, stopping services gracefully..."

    # Stop queue worker
    if [ ! -z "$WORKER_PID" ]; then
        kill -TERM $WORKER_PID 2>/dev/null || true
        wait $WORKER_PID 2>/dev/null || true
        echo "Queue worker stopped."
    fi

    # Stop health server
    if [ ! -z "$HEALTH_SERVER_PID" ]; then
        kill -TERM $HEALTH_SERVER_PID 2>/dev/null || true
        echo "Health server stopped."
    fi

    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Start health check server first
echo "Starting health check server..."
create_health_server

# Wait a moment for health server to start
sleep 2

# Verify health server is running
if ! kill -0 $HEALTH_SERVER_PID 2>/dev/null; then
    echo "ERROR: Health server failed to start!"
    exit 1
fi

echo "Health server started successfully on port ${PORT:-8080}"

# Start queue worker in work (not listen) mode
echo "Starting queue worker service..."
php artisan queue:work \
    --queue="${QUEUE_NAME:-default}" \
    --sleep=3 \
    --tries=3 \
    --backoff=3 \
    --max-time=0 \
    --memory=512 \
    --verbose &

WORKER_PID=$!

# Log status periodically and monitor worker
while true; do
    if ! kill -0 $WORKER_PID 2>/dev/null; then
        echo "Queue worker process died, restarting..."
        php artisan queue:work \
            --queue="${QUEUE_NAME:-default}" \
            --sleep=3 \
            --tries=3 \
            --backoff=3 \
            --max-time=0 \
            --memory=512 \
            --verbose &
        WORKER_PID=$!
    fi

    # Check if health server is still running
    if ! kill -0 $HEALTH_SERVER_PID 2>/dev/null; then
        echo "Health server died, restarting..."
        create_health_server
    fi

    echo "Queue worker running. Status check at $(date)"
    php artisan queue:monitor
    sleep 300  # Check every 5 minutes
done