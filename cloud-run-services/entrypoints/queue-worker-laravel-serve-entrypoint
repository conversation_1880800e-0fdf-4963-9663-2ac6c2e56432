#!/bin/bash

# Laravel Queue Worker Service Entrypoint with Laravel Serve
# This script runs both Laravel serve (for health checks) and queue workers

set -e

echo "Starting Laravel Queue Worker Service with HTTP Server..."
echo "Timestamp: $(date)"
echo "Environment: $APP_ENV"
echo "Queue: ${QUEUE_NAME:-default}"
echo "Port: ${PORT:-8080}"
echo "Working directory: $(pwd)"
echo "PHP version: $(php --version | head -1)"

# Set proper permissions
chmod -R 755 /var/www/html/storage /var/www/html/bootstrap/cache

# Clear any cached configuration to ensure fresh environment
echo "Clearing Laravel caches..."
php artisan config:clear

# Cache configuration for better performance
echo "Caching configuration..."
php artisan config:cache

# Check database connection
echo "Testing database connection..."
php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connection successful';"

# Function to handle graceful shutdown
cleanup() {
    echo "Received shutdown signal, stopping services gracefully..."
    
    # Stop Laravel serve
    if [ ! -z "$SERVE_PID" ]; then
        kill -TERM $SERVE_PID 2>/dev/null || true
        wait $SERVE_PID 2>/dev/null || true
        echo "Laravel serve stopped."
    fi
    
    # Stop queue worker
    if [ ! -z "$WORKER_PID" ]; then
        kill -TERM $WORKER_PID 2>/dev/null || true
        wait $WORKER_PID 2>/dev/null || true
        echo "Queue worker stopped."
    fi
    
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Start Laravel serve for health checks
echo "Starting Laravel HTTP server on port ${PORT:-8080}..."
php artisan serve --host=0.0.0.0 --port=${PORT:-8080} &
SERVE_PID=$!

# Wait a moment for Laravel serve to start
sleep 3

# Verify Laravel serve is running
if ! kill -0 $SERVE_PID 2>/dev/null; then
    echo "ERROR: Laravel serve failed to start!"
    exit 1
fi

echo "Laravel HTTP server started successfully on port ${PORT:-8080}"

# Start queue worker
echo "Starting queue worker..."
php artisan queue:work \
    --queue="${QUEUE_NAME:-default}" \
    --sleep=3 \
    --tries=3 \
    --backoff=3 \
    --max-time=0 \
    --memory=512 \
    --verbose &

WORKER_PID=$!

echo "Queue worker started with PID: $WORKER_PID"
echo "Laravel serve running with PID: $SERVE_PID"

# Monitor both processes
while true; do
    # Check if Laravel serve is still running
    if ! kill -0 $SERVE_PID 2>/dev/null; then
        echo "Laravel serve died, restarting..."
        php artisan serve --host=0.0.0.0 --port=${PORT:-8080} &
        SERVE_PID=$!
    fi
    
    # Check if queue worker is still running
    if ! kill -0 $WORKER_PID 2>/dev/null; then
        echo "Queue worker process died, restarting..."
        php artisan queue:work \
            --queue="${QUEUE_NAME:-default}" \
            --sleep=3 \
            --tries=3 \
            --backoff=3 \
            --max-time=0 \
            --memory=512 \
            --verbose &
        WORKER_PID=$!
    fi
    
    echo "Services running. Status check at $(date)"
    echo "Laravel serve PID: $SERVE_PID, Queue worker PID: $WORKER_PID"
    
    # Optional: Check queue status
    php artisan queue:monitor 2>/dev/null || echo "Queue monitor unavailable"
    
    sleep 300  # Check every 5 minutes
done
