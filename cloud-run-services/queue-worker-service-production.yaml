apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: queue-worker-service
  labels:
    app: freshcar-core
    component: queue-worker
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cloudsql-instances: CLOUD_SQL_CONNECTION_NAME
    spec:
      serviceAccountName: tlg-devops@PROJECT_ID.iam.gserviceaccount.com
      containers:
        - name: laravel-queue-worker
          image: gcr.io/PROJECT_ID/api:production-latest
          command:
            [
              "/var/www/html/cloud-run-services/entrypoints/queue-worker-entrypoint",
            ]
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
          env:
            - name: APP_ENV
              value: "production"
            - name: APP_DEBUG
              value: "false"
            - name: LOG_CHANNEL
              value: "stderr"
            - name: DB_CONNECTION
              value: "pgsql"
            - name: DB_PORT
              value: "5432"
            - name: DB_SOCKET
              value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
            - name: DB_DATABASE
              value: "DB_DATABASE"
            - name: DB_USERNAME
              value: "DB_USERNAME"
            - name: CACHE_DRIVER
              value: "database"
            - name: QUEUE_CONNECTION
              value: "database"
            - name: QUEUE_NAME
              value: "default"
            - name: APP_KEY
              valueFrom:
                secretKeyRef:
                  name: production-app-key
                  key: latest
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: production-db-password
                  key: latest
