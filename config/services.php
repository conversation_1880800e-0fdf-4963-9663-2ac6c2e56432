<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'eu-west-2'),
    ],
    'gohighlevel' => [
        'client_id' => env('GOHIGHLEVEL_CLIENT_ID'),
        'client_secret' => env('GOHIGHLEVEL_CLIENT_SECRET'),
        'private_integration_token' => env('GOHIGHLEVEL_PRIVATE_INTEGRATION_TOKEN'),
        'client_code' => env('GOHIGHLEVEL_CODE'),
        'redirect_uri' => env('GOHIGHLEVEL_REDIRECT_URI'),
        'base_url_authorization' => env('GOHIGHLEVEL_BASE_URL_AUTHORIZATION'),
        'base_url_api' => env('GOHIGHLEVEL_BASE_URL_API')
    ],
    'freshcarapi' => [
        'url' => env('FRESHCARAPI_URL'),
        'admin_user' => env('FRESHCARAPI_ADMIN_USER'),
        'admin_password' => env('FRESHCARAPI_ADMIN_PASSWORD'),
        'admin_token' => env('FRESHCARAPI_ADMIN_TOKEN'),
    ],

    /*
    |--------------------------------------------------------------------------
    | DVLA API
    |--------------------------------------------------------------------------
    */
    'dvla' => [
        'api_key' => env('DVLA_API_KEY'),
        'api_url' => env('DVLA_API_URL'),
        'mot_history' => [
            'api_key' => env('DVLA_MOT_HISTORY_API_KEY'),
            'client_id' => env('DVLA_MOT_HISTORY_API_CLIENT_ID'),
            'client_secret' => env('DVLA_MOT_HISTORY_API_CLIENT_SECRET'),
            'scope' => env('DVLA_MOT_HISTORY_API_SCOPE_URL'),
            'token_url' => env('DVLA_MOT_HISTORY_API_TOKEN_URL'),
            'tenant_id' => env('DVLA_MOT_HISTORY_API_TENANT_ID'),
            'api_url' => env('DVLA_MOT_HISTORY_API_URL'),
        ],
    ],

];
