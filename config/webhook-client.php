<?php

return [
    'configs' => [
        [
            'name' => 'gohighlevel-opportunity-status-update',
            'signing_secret' => env('GOHIGHLEVEL_WEBHOOK_SECRET_PATH', 'gohighlevel_key.pub'),
            'signature_header_name' => 'x-wh-signature',
            'signature_validator' => \App\Services\GoHighLevel\WebhookHandlers\SignatureHandler::class,
            'webhook_profile' => \Spatie\WebhookClient\WebhookProfile\ProcessEverythingWebhookProfile::class,
            'webhook_response' => \Spatie\WebhookClient\WebhookResponse\DefaultRespondsTo::class,
            'webhook_model' => \Spatie\WebhookClient\Models\WebhookCall::class,
            'store_headers' => [],

            /*
             * The class name of the job that will process the webhook request.
             *
             * This should be set to a class that extends \Spatie\WebhookClient\Jobs\ProcessWebhookJob.
             */
            'process_webhook_job' => \App\Services\GoHighLevel\WebhookHandlers\OpportunityStatusUpdateHandler::class,
        ],
        [
            'name' => 'gohighlevel-webhook-default',
            'signing_secret' => env('GOHIGHLEVEL_WEBHOOK_SECRET_PATH', 'gohighlevel_key.pub'),
            'signature_header_name' => 'x-wh-signature',
            'signature_validator' => \App\Services\GoHighLevel\WebhookHandlers\SignatureHandler::class,
            'webhook_profile' => \Spatie\WebhookClient\WebhookProfile\ProcessEverythingWebhookProfile::class,
            'webhook_response' => \Spatie\WebhookClient\WebhookResponse\DefaultRespondsTo::class,
            'webhook_model' => \Spatie\WebhookClient\Models\WebhookCall::class,
            'store_headers' => [],

            /*
             * The class name of the job that will process the webhook request.
             *
             * This should be set to a class that extends \Spatie\WebhookClient\Jobs\ProcessWebhookJob.
             */
            'process_webhook_job' => \App\Services\GoHighLevel\WebhookHandlers\DefaultHandler::class,
        ],
    ],

    /*
     * The integer amount of days after which models should be deleted.
     *
     * It deletes all records after 30 days. Set to null if no models should be deleted.
     */
    'delete_after_days' => 30,

    /*
     * Should a unique token be added to the route name
     */
    'add_unique_token_to_route_name' => false,
];
